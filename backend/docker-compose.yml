version: '3'
services:
  backend-app:
    build: .
    image: backend-app
    container_name: backend-app
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem:/etc/letsencrypt/live/${DOMAIN}/fullchain.pem"
      - "/etc/letsencrypt/live/${DOMAIN}/privkey.pem:/etc/letsencrypt/live/${DOMAIN}/privkey.pem"
      - ./htpasswd:/etc/nginx/.htpasswd:ro
    environment:
      - USE_DB_LOGGER=True
      - SERVER_WORKERS=${SERVER_WORKERS:-4}

volumes:
  htpasswd_storage:
def checkIfDockerImageExists(dockerImages, targetDockerImageName) {
    for(dockerImage in dockerImages) {
        if(dockerImage ==~ targetDockerImageName) {
            return [exists: true, dockerImage: dockerImage]
        }
    }
    return [exists: false, dockerImage: null]
}

def getDockerImageName(image) {
    return image.split(/\s{2,}/)[0].trim()
}

def extractUsernameAndPassword(credentialsId) {
    withCredentials([usernamePassword(credentialsId: credentialsId, usernameVariable: "USERNAME", passwordVariable: "PASSWORD")]) {
        return ["${USERNAME}", "${PASSWORD}"]
    }
}

def extractData(credentialId) {
    withCredentials([string(credentialsId: credentialId, variable: "CREDENTIAL")]) {
        return "${CREDENTIAL}"
    }
}

def removeFileExtension(file) {
    return file.split("\\.")[0]
}

pipeline {
    agent any

    environment {
        GITHUB_CREDENTIALS_ID = "248185fc-432c-4058-931d-1f249bc1f39f"
        DIGITAL_OCEAN_ACCESS_TOKEN = "237c218c-d06e-4543-bb1c-5a796ded68f5"
        SSH_CREDENTIALS = "d566cd8f-ad81-4376-9e02-10312ec491e0"
    }
    
    parameters {
        string(
            name: "github_repository", 
            defaultValue: "https://github.com/CloudIB/cloud-ib-database-backend.git", 
            description: "Specify the GitHub repository to clone the project from."
        )
        string(
            name: "github_branch_name", 
            defaultValue: "main", 
            description: "Specify the branch to clone the project from."
        )
        string(
            name: "instance_host", 
            defaultValue: "*************",
            description: "The hostname or IP address of the DigitalOcean droplet instance."
        )
        string(
            name: "image_name", 
            defaultValue: "backend", 
            description: "The name of the Docker image to be used or created. The final pushed image name will be: {env.image_name_env}_{GITHUB_BRANCH_NAME}_{lastGitCommitHash}:latest"
        )
        string(
            name: "registry_name",
            defaultValue: "registry.digitalocean.com/cloud-ib",
            description: "The name of the container registry where the Docker image will be pushed. Ensure this matches the registry in your DigitalOcean account."
        )
        string(
            name: "host_and_container_port",
            defaultValue: "80",
            description: "Port number for both host and docker container binding on the DigitalOcean droplet instance."
        )
    }
    
    stages {
        stage("Build") {
            steps {
                script {
                    git branch: "${env.github_branch_name}", url: "${env.github_repository}", credentialsId: env.GITHUB_CREDENTIALS_ID
                    
                    def lastGitCommitMessage = sh(returnStdout: true, script: "git log -1 --pretty=%B").trim()
                    def lastGitCommitHash = sh(returnStdout: true, script: "git rev-parse HEAD").trim()
                    
                    dir("backend") {
                        def dockerImages = sh(script: "docker image ls", returnStdout: true).trim().split("\n")
                        dockerImages = dockerImages[1..-1]
                        def targetDockerImageName = "${env.registry_name}/${env.image_name}_.+"
                        def dockerImageResult = checkIfDockerImageExists(dockerImages, targetDockerImageName)
                        
                        if(dockerImageResult.exists) {
                            def dockerImageName = getDockerImageName(dockerImageResult.dockerImage)
                            sh "docker rmi -f ${dockerImageName}"
                        } 
                        
                        env.IMAGE_NAME = "${env.image_name}_${env.GITHUB_BRANCH_NAME}_${lastGitCommitHash}:latest"
                        sh "docker build -t ${env.IMAGE_NAME} ."
                        sh 'docker images' 
                        
                        withCredentials([string(credentialsId: env.DIGITAL_OCEAN_ACCESS_TOKEN, variable: 'TOKEN')]) {
                          sh script: """
                            doctl auth init --access-token "$TOKEN"
                            doctl registry login
                          """
                          
                          sh "docker tag ${env.IMAGE_NAME} ${env.registry_name}/${env.IMAGE_NAME}"
                          sh "docker push ${env.registry_name}/${env.IMAGE_NAME}"
                        }
                    }
                }
            }
        }
        stage("Deploy") {
            steps {
                script {
                    def (SSH_USERNAME, SSH_PASSWORD) = extractUsernameAndPassword(env.SSH_CREDENTIALS)
                    def TOKEN = extractData(env.DIGITAL_OCEAN_ACCESS_TOKEN)
                    def (GIT_USERNAME, GIT_PAT_PASSWORD) = extractUsernameAndPassword(env.GITHUB_CREDENTIALS_ID)
                    
                    def repositoryName = "${env.github_repository}".split("/")[-1]
                    repositoryName = removeFileExtension(repositoryName)
                    def branchUrl = "https://${GIT_USERNAME}:${GIT_PAT_PASSWORD}@github.com/CloudIB/${repositoryName}"
                    
                    def sshToInstanceCommand = """
                        sshpass -p '${SSH_PASSWORD}' ssh -T -o StrictHostKeyChecking=no ${SSH_USERNAME}@${env.instance_host} << 'EOF'
                        echo "SSH-ed into the digital ocean instance"
                    """
                    
                    def installDockerCommand = """
                        snap install docker
                    """
                    
                    def installDoctlCommand = """
                        snap install doctl
                    """
                    
                    def setupDoctlCommand = """
                        snap connect doctl:dot-docker
                        doctl auth init --access-token "$TOKEN"
                        doctl registry login
                    """
                    
                    def cloneRepositoryCommand = """
                        git clone ${branchUrl} -b ${env.github_branch_name}
                    """
                    
                    def moveToProjectRootDirectory = """
                        cd ${repositoryName}/backend
                        ls -l
                    """
                    
                    def stopRunningContainersCommand = """
                        docker compose down
                    """
                    
                    def pulledImageName = "${env.registry_name}/${env.IMAGE_NAME}"
                    def pullImageCommand = """
                        docker pull ${pulledImageName}
                        docker images 
                    """
                    
                    def exportDockerEnvParamsCommand = """
                        export IMAGE_NAME=${pulledImageName}
                        export PORT=${env.host_and_container_port}
                    """
                    
                    def startContainerCommand = """
                        docker-compose -f docker-compose.deploy.yml up
                    """
                    
                    def cleanupInstanceCommand = """
                        echo "Removing code repository: ${repositoryName}"
                        rm -rf ${repositoryName} 
                    """
                    
                    def goBackToRootCommand = """
                        cd ../..
                    """
                    
                    sh """
                        set -e
                        ${sshToInstanceCommand}
                        ${installDockerCommand}
                        ${installDoctlCommand}
                        ${setupDoctlCommand}
                        ${cloneRepositoryCommand}
                        ${moveToProjectRootDirectory}
                        ${stopRunningContainersCommand}
                        ${pullImageCommand}
                        ${exportDockerEnvParamsCommand}
                        ${startContainerCommand}
                        ${goBackToRootCommand}
                        ${cleanupInstanceCommand}
                    """
                }
            }
        }
    }

    post {
        success {
            echo "Images successfully built and pushed to DigitalOcean registry!"
        }
        failure {
            echo "Build failed! Check the Jenkins logs for details."
        }
    }
}
FROM python:3.11-slim-bookworm

# Set environment variables to ensure Python outputs logs to the console
ENV PYTHONUNBUFFERED 1

# Install curl for healthcheck
RUN apt-get update && \ 
    apt install curl -y

# Set the working directory
WORKDIR /app

COPY . /app

RUN \
    pip install --no-cache-dir -r requirements.txt && \
    # Set permissions for the log directories
    mkdir -p /var/log/gunicorn && chmod -R 755 /var/log/gunicorn

EXPOSE 5000

CMD gunicorn src.app:app \
    --bind 0.0.0.0:5000 \
    --access-logfile /var/log/gunicorn/access.log \
    --error-logfile /var/log/gunicorn/error.log \
    --workers 2
    # --timeout 90 & \

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=2 CMD curl --fail http://localhost:5000 > /dev/null 2>&1 || exit 1
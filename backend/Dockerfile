FROM python:3.11-slim-bookworm

RUN \
    # Install Nginx    
    apt-get update && apt-get install -y nginx && \
    # Install curl for healthcheck
    apt install curl -y

# Set environment variables to ensure Python outputs logs to the console
ENV PYTHONUNBUFFERED 1

WORKDIR /app

COPY . /app

RUN \
    pip install --no-cache-dir -r requirements.txt && \
    # Remove default nginx site
    rm /etc/nginx/sites-enabled/default && \
    # Set permissions for the log directories
    mkdir -p /var/log/gunicorn && chmod -R 755 /var/log/gunicorn

# Copy custom nginx configuration to the container
COPY nginx.conf /etc/nginx/sites-enabled/

EXPOSE 80 443 5000

CMD gunicorn src.app:app \
    --bind 0.0.0.0:5000 \
    --access-logfile /var/log/gunicorn/access.log \
    --error-logfile /var/log/gunicorn/error.log \
    --workers ${SERVER_WORKERS} \
    --timeout 90 & \
    nginx -g 'daemon off;'

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=2 CMD curl --fail http://localhost:5000 > /dev/null 2>&1 || exit 1
from itertools import product


def generate_variants(word):
    """
    Generate possible search variants by replacing special characters with their ASCII equivalents
    and vice versa (e.g., "<PERSON><PERSON><PERSON>" <=> "Baer").
    """
    replacements = {
        "ß": "ss",
        "ss": "ß",
        "ä": "ae",
        "ae": "ä",
        "ö": "oe",
        "oe": "ö",
        "ü": "ue",
        "ue": "ü"
    }

    variants = {word}  # Use a set to avoid duplicates

    for original, replacement in replacements.items():
        if original in word:
            variants.add(word.replace(original, replacement))
        if replacement in word:
            variants.add(word.replace(replacement, original))

    return list(variants)




#print(generate_variants("Straße"))
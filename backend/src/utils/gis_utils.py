import math
import geo<PERSON><PERSON>


def create_geojson_circle(lat, lon, radius_km=100, num_points=100):
    # Earth's radius in kilometers
    earth_radius_km = 6371.0

    # Convert radius from kilometers to radians
    radius_radians = radius_km / earth_radius_km

    # Generate points around the circle
    points = []
    for i in range(num_points + 1):  # +1 to close the loop
        angle = i * (2 * math.pi / num_points)
        point_lat = lat + (radius_radians * math.cos(angle)) * (180 / math.pi)
        point_lon = lon + (radius_radians * math.sin(angle) / math.cos(math.radians(lat))) * (180 / math.pi)
        points.append((point_lon, point_lat))

    # Create GeoJSON Feature for the circle
    circle_feature = geojson.Feature(
        geometry=geojson.Polygon([points]),
        properties={"radius_km": radius_km}
    )

    # Wrap it in a FeatureCollection
    to_ret = geojson.FeatureCollection([circle_feature])
    return geojson.dumps(to_ret, indent=2)

# Example usage:
#lat, lon = 40.7128, -74.0060  # Example coordinates (New York City)
#geojson_circle = create_geojson_circle(lat, lon)

# Print or save to a file
#print(geojson.dumps(geojson_circle, indent=2))

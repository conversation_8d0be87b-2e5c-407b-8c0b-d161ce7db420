from dataclasses import dataclass
import os
from pathlib import Path

#ROOT_DIRECTORY : str = os.getcwd()

# Define the path to the transactions file
#PATH_TRANSACTIONS = os.path.join(ROOT_DIRECTORY, "data", "export_All-transactions-modified--_2024-07-04_12-15-44.csv")

# a bit of a hack
if "src" in os.getcwd():
    ROOT_DIRECTORY = os.path.dirname(os.getcwd())  # Move up one level
    #ROOT_DIRECTORY = os.path.dirname(os.path.dirname(os.getcwd()))
else:
    ROOT_DIRECTORY = os.getcwd()
    # ROOT_DIRECTORY = os.getcwd() + "/backend"


PATH_TRANSACTIONS = os.path.join(ROOT_DIRECTORY, "data", "2025-09-03_2_export.csv")
PATH_REAL_ESTATE_CITIES = os.path.join(ROOT_DIRECTORY, "data", "Real Estate Portfolio Part v17_OutputCity.csv")
PATH_REAL_ESTATE_NOTES = os.path.join(ROOT_DIRECTORY, "data", "Real Estate Portfolio Part v17_OutputNotes.csv")

PATH_SECTORS2MAP = os.path.join(ROOT_DIRECTORY, "data", "sector2map.csv")

PATH_YAML_CONFIG = os.path.join(ROOT_DIRECTORY, "data", "Presentation.yml")
#PATH_YAML_CONFIG = os.path.join(ROOT_DIRECTORY, "..", "data", "Presentation.yml")

#ROOT_DIRECTORY = "/Users/<USER>/Documents/GitHub/cloud-ib-database-backend/backend"


PATH_WEALTH_MANAGEMENT_DATA = os.path.join(ROOT_DIRECTORY, "data", "WealthManagementUSA_AUMInfov6.csv")
PATH_ASSET_MANAGEMENT_DATA = os.path.join(ROOT_DIRECTORY, "data", "AlternativeAssetManagement_AUM_v4.csv")
CARS = os.path.join(ROOT_DIRECTORY, "data", "Retailing-Auto_Normalized_manual_current.csv")
IT_CONSULTING_SUPPLIERS = os.path.join(ROOT_DIRECTORY, "data", "software_brands_results_normalized_output_manual_v2.csv")
ECOMMERCE_BRANDS = os.path.join(ROOT_DIRECTORY, "data", "EcommerceBrands_consolidated_v2.csv")



HOSPITALS = os.path.join(ROOT_DIRECTORY, "data", "KIS_providers_2025-03-17.csv")
BANKS = os.path.join(ROOT_DIRECTORY, "data", "CoreBankingSystem_providers_2025-03-18.csv")



HOSPITALS_SPECIALIZATIONS = os.path.join(ROOT_DIRECTORY, "data", "HospitalsSpecializations.csv")
HOSPITALS_SPECIALIZATIONS_LARGE = os.path.join(ROOT_DIRECTORY, "data", "Hospitals_Comparison_final.csv")

#PATH_TRANSACTIONS = "/Users/<USER>/Documents/GitHub/cloud-ib-database-backend/backend/data/Aggregated data - bubble.csv"

REGION_HEATMAP_LABELS = os.path.join(ROOT_DIRECTORY, "data", "region_heat_map_labels4.csv")
REGION_HEATMAP_CATEGORIES = os.path.join(ROOT_DIRECTORY, "data", "region_heat_map_categories4.csv")


STRIP_PROFILES = os.path.join(ROOT_DIRECTORY, "data", "Strip_Profile_Data_WM-Tax_v3-7_20250612_2(Summary_values).csv")

WHITELIST = os.path.join(ROOT_DIRECTORY, "data", "PerrmittedFirmsList_2025-04-22.v2.csv")

SPECIAL_COMPANIES_WITH_SUBSECTORS  = os.path.join(ROOT_DIRECTORY, "data", "GermanyInsBrokers_20250509_classified.csv")

INSURANCE_BROKER_NUM_EMPLOYEES  = os.path.join(ROOT_DIRECTORY, "data", "InsuranceBrokerGermany_Employees.csv")

ANTITRUST_DUMMY_DATA_MARKET_SHARE = os.path.join(ROOT_DIRECTORY, "data", "antitrust_mkt_share.csv")
ANTITRUST_DUMMY_DATA_OVERLAPS = os.path.join(ROOT_DIRECTORY, "data", "antitust_overlaps.csv")
CIB_OVERVIEW_DUMMY_DATA = os.path.join(ROOT_DIRECTORY, "data", "target_search_overview_panel_example.csv")


@dataclass
class ZoomFilepaths:
    boundaries: Path = Path(f"{ROOT_DIRECTORY}/data/zoom/country_data_from_natural_earth.csv")
    missing_countries: Path = Path(f"{ROOT_DIRECTORY}/data/zoom/Missing_Countries_Suggestions.csv")


zoom_filepaths = ZoomFilepaths()

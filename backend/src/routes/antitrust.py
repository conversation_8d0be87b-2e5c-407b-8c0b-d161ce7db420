from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.Clickhouse_connector as cc
from enum import Enum

import src.utils.paths.csv_paths as csv_paths
import pandas as pd
import json
import os


import json
from flask_restx import fields

from src.routes.routes_common import orient_dict
import src.analysis.antitrust as antitrust


def register_routes_antitrust(api, auth):
    target_request_model_fields = {
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'country_code': fields.Integer(
            required=True,
            description='Country code represented as an integer.',
            example=63
        ),
        'other_locations': fields.List(
            fields.String,
            required=True,
            description='List of other locations.',
            example=['a395329c-292c-4ea2-9ba7-6f756a57516b', '49abaa59-80e5-42cc-b757-0bcd6f00a091', '8c051a2c-d2ac-4fe3-916a-f51a122dd0aa',
                     '533ee03a-03be-4943-aa92-4f40f52fca50', '02535618-2cfe-4d30-8878-51ebb9348495', '2257eb2e-82b0-48b7-9e91-82220d03be7c',
                     'ae78fd1b-de41-48f0-aa7e-d4f2c5aade4a', '64ae7f6c-41e0-42e6-8ce9-72b00be429e4', 'f4e7346b-04ef-484f-ba11-78b95693ce90',
                     '5785a374-6da0-42aa-9659-c9a40bde8e56', '8b57d04c-f90d-4b92-87f9-fa6eeaca4a9d', 'd3d4d366-cede-44c4-83ef-27f36d227ea1',
                     '08006cf5-c31f-4b8e-b0f2-c365d853bc2e', '2895c609-cd11-4813-89ea-e4b083efd166', '90a9a1d2-179c-4740-89a1-2f770f5548e9',
                     'fd43b309-8626-4ab9-a7ea-bbefa931eff2', 'cabe253e-07d2-4b6f-88eb-057103828f6f', '6ae5ee29-6688-429d-94af-4037f11be909',
                     '9449b016-7140-433d-be6c-351dd974360e', '56a885b7-e009-48eb-ad91-04600e81bec3', '705612bb-9c75-402d-bf4d-54dcde4a5a87',
                     'd3e5c8d5-7801-4cc4-9a7b-7269e05145a5']
        )}

    ns = api.namespace('antitrust', description='Antitrust analysis queries.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    target_company_analysis_fields = {
        'target_cib_id': fields.String(
            required=True,
            description='CIB ID of the target.',
            example='haslinger-immobilien.de'
        ),
        'radius_km': fields.Integer(
            required=True,
            description='Radius around target locations in km.',
            example=150
        ),
        **target_request_model_fields  # Ensure this is a dictionary
    }

    @ns.route('/target_company_analysis')
    class target_company_analysis(Resource):
        @ns.doc('target_company_analysis')
        @auth.login_required
        @api.expect(api.model('TargetCompanyRequest', target_company_analysis_fields))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                target_cib_id = data.get('target_cib_id', 'haslinger-immobilien.de')
                country_code = data.get('country_code', 63)
                other_locations = data.get('other_locations', [])
                radius_km = data.get('radius_km', 150)

                current_app.logger.info(f"{request.url} :: {data}")
                result = antitrust.get_overlapping_locations(buyer_cib_id, target_cib_id, other_locations, country_code, radius_km)

                json_str = json.dumps(result)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    target_location_analysis_fields = {
        'target_location_id': fields.String(
            required=True,
            description='ID of the target location to analyze.',
            example='cbbbea5a-c38e-43ed-ace5-64da8b3b15db'
        ),
        'target_cib_id': fields.String(
            required=True,
            description='CIB ID of the target.',
            example='haslinger-immobilien.de'
        ),
        'isochrone_time': fields.Integer(
            required=True,
            description='Isochrone time in minutes, must be one of [15, 30, 60].',
            example=60,
            enum=[15, 30, 60]  # Enum values for isochrone_time
        ),
        'tolerance': fields.Float(
            required=True,
            description='Simplify isochrones with a tolerance value, units are in CRSs measurement.',
            example=0.01
        ),
        **target_request_model_fields  # Ensure this is a dictionary
    }


    @ns.route('/target_location_analysis')
    class target_location_analysis(Resource):
        @ns.doc('target_location_analysis')
        @auth.login_required
        @api.expect(api.model('TargetLocationRequest', target_location_analysis_fields))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                target_cib_id = data.get('target_cib_id', 'haslinger-immobilien.de')
                target_location_id = data.get('target_location_id', 'd3e5c8d5-7801-4cc4-9a7b-7269e05145a5')
                isochrone_time = data.get('isochrone_time', 15)
                country_code = data.get('country_code', 63)
                other_locations = data.get('other_locations', [])
                tolerance = data.get('tolerance', 0.01)

                current_app.logger.info(f"{request.url} :: {data}")

                isochrones_result, market_analysis_result = antitrust.get_isochrone_analysis(target_location_id, buyer_cib_id, target_cib_id, other_locations, country_code, isochrone_time, tolerance)

                json_str = isochrones_result.to_json()

                json_str_dict = json.loads(json_str)

                json_str_dict['market_analysis'] = market_analysis_result

                return Response(json.dumps(json_str_dict, indent=2), mimetype='application/geo+json')
            except Exception as e:
                return log_and_return_error(e)


    market_share_analysis_fields = {
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'target_cib_id': fields.String(
            required=True,
            description='CIB ID of the target.',
            example='haslinger-immobilien.de'
        ),
        'sectors': fields.List(
            fields.Integer,
            required=True,
            description='Sectors for extra companies to include.',
            example=[4]
        ),
        'other_cib_ids': fields.List(
            fields.String,
            required=True,
            description='CIB IDs of other companies.',
            example=['vestas.com']
        ),
        'country_code': fields.Integer(
            required=True,
            description='Country code represented as an integer.',
            example=63
        ),
        'target_location_id': fields.String(
            required=True,
            description='ID of the target location to analyze.',
            example='cbbbea5a-c38e-43ed-ace5-64da8b3b15db'
        ),
        'radius_km': fields.Integer(
            required=True,
            description='Radius around target locations in km.',
            example=150
        ),

    }

    @ns.route('/market_share_analysis')
    class market_share_analysis(Resource):
        @ns.doc('market_share_analysis')
        @auth.login_required
        @api.expect(api.model('MarketShareRequest', market_share_analysis_fields))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                target_cib_id = data.get('target_cib_id', 'haslinger-immobilien.de')
                selected_sectors = data.get('sectors', [])
                other_cib_ids = data.get('other_cib_ids', [])
                country_code = data.get('country_code', 63)
                target_location_id = data.get('target_location_id', 'd3e5c8d5-7801-4cc4-9a7b-7269e05145a5')
                radius_km = data.get('radius_km', 150)

                current_app.logger.info(f"{request.url} :: {data}")

                result = antitrust.get_nationwide_market_share(buyer_cib_id, target_cib_id, selected_sectors, other_cib_ids, country_code, target_location_id, radius_km)

                return Response(json.dumps(result, indent=2), mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_allowed_sectors')
    class get_allowed_sectors(Resource):
        @ns.doc('get_allowed_sectors')
        @auth.login_required
        @api.doc(params={**{
        }})
        def get(self):
            try:

                current_app.logger.info(f"{request.url}")

                # 41 WM / brokerage
                # 34 Retailing - Food
                # 7, 43, 44, 45, 46, 47, 48, 49 - Banks...
                # 22 Insurance Brokers
                # 4 Apparel Retailers
                # 85 Outlet centers

                result = [41,
                          34,
                          7, 43, 44, 45, 46, 47, 48, 49,
                          22,
                          4, 85]

                return Response(json.dumps(result), mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)




    table_api_input = {
        'analysis_type': fields.String(
            required=True,
            description='OVERLAP | MARKET_SHARE',
            example='OVERLAP | MARKET_SHARE'
        ),
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'target_cib_id': fields.String(
            required=True,
            description='CIB ID of the target.',
            example='haslinger-immobilien.de'
        ),
        'target_location_id': fields.String(
            required=True,
            description='ID of the target location to analyze.',
            example='cbbbea5a-c38e-43ed-ace5-64da8b3b15db'
        ),
        'other_cib_ids': fields.List(
            fields.String,
            required=True,
            description='CIB IDs of other companies.',
            example=['vestas.com']
        ),
        'country_code': fields.Integer(
            required=True,
            description='Country code represented as an integer.',
            example=63
        ),
        'radius_km': fields.Integer(
            required=True,
            description='Radius around target locations in km.',
            example=150
        ),
        'orient': fields.String(
            description='DF format',
            required=True,
            default='records')
    }

    @ns.route('/antitrust_table')
    class antitrust_table(Resource):
        @ns.doc('antitrust_table')
        @auth.login_required
        @api.expect(api.model('TableRequest', table_api_input))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                analysis_type = data.get('analysis_type', 'OVERLAP')
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                target_cib_id = data.get('target_cib_id', 'haslinger-immobilien.de')
                target_location_id = data.get('target_location_id', 'xxx')
                other_cib_ids = data.get('other_cib_ids', [])
                country_code = data.get('country_code', 63)
                radius_km = data.get('radius_km', 150)
                orient = data.get('orient', 'records')

                current_app.logger.info(f"{request.url} :: {data}")

                if analysis_type == 'OVERLAP':
                    overlap_df = antitrust.get_buyer_target_overlaps(buyer_cib_id, target_cib_id, radius_km * 1000, country_code)
                    result = antitrust.transform_overlap_df(overlap_df)
#                    result = pd.read_csv(csv_paths.ANTITRUST_DUMMY_DATA_OVERLAPS, sep=",")

                    df = antitrust.get_locations_and_distances(buyer_cib_id, target_cib_id, radius_km, country_code)
                    overall = antitrust.get_locations_to_remove_overall(df)

                    target_divestiture = antitrust.get_buyer_target_overlaps(buyer_cib_id, target_cib_id,radius_km * 1000, country_code)
                    target_divestiture = target_divestiture[target_divestiture['region_type'] == 'country'].iloc[0]['within_count']

                    buyer_divestiture = antitrust.get_buyer_target_overlaps(target_cib_id, buyer_cib_id, radius_km * 1000, country_code)
                    buyer_divestiture = buyer_divestiture[buyer_divestiture['region_type'] == 'country'].iloc[0]['within_count']

                    rows_to_add = [
                        ["Required divestiture of locations to avoid overlap",pd.NA,True,False],
                        ["Divestiture of target locations only",target_divestiture,False, False],
                        ["Divestiture of buyer locations only",buyer_divestiture,False, False],
                        ["Divestiture of both Buyer and Target locations",len(overall),False, True]
                    ]

                    new_df = pd.DataFrame(rows_to_add, columns=result.columns)
                    result = pd.concat([result, new_df], ignore_index=True)

                else:
                    result = pd.read_csv(csv_paths.ANTITRUST_DUMMY_DATA_MARKET_SHARE, sep=",")

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)
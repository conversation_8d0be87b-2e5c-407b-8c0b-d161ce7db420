from flask import request, Response, send_file, current_app
from flask_restx import Resource, fields
import src.data_platform.DB_connector as db
import src.data_platform.CSV_connector as CSV_loader
import src.analysis.transactions as tr

from src.auth import role_required


import json

from src.routes.routes_common import orient_dict, paginate_dataframe
from io import BytesIO
from enum import Enum

class DataType(Enum):
    PRESENTATION = "Investor presentation"
    OTHER = "Other"

class Version(Enum):
    FULL = "full"
    LITE = "lite"

class DocProp(Enum):
    DOCUMENT = "document"
    LABEL = "label"

table_model = {
    'deal_ids': fields.List(
        fields.String,
        required=True,
        description='List of DealIDs.',
        example=['SSB-US-20170427-01','IF-EU-20250108-01','STLA-EU-20190527-01','NESN-EU-20100105-01']
    ),
    'orient': fields.String(
        required=False,
        description='Specifies the format of the JSON output.',
        example='records',
        default='records',
        enum=['split', 'records', 'index', 'columns', 'values', 'table']
    ),
    #'column_names': fields.List(
    #    fields.String,
    #    required=True,
    #    description='List of column names.',
    #    example=['Investor presentation', 'CostSynergiesAbs', 'integration_cost_abs']
    #)
}

def register_routes_transactions(api, auth):
    ns = api.namespace('transactions', description='Transaction data.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')


    @ns.route('/get_all_transactions_for_chatbot')
    class get_all_transactions_for_chatbot(Resource):
        @ns.doc('get_all_transactions_for_chatbot')
        @api.doc(params={**{
            'search': {'in': 'query', 'description': 'Search by target or buyer name.', 'type': 'string',
                       'required': False, 'example': 'Foo'},
            'sector': {'in': 'query', 'description': 'The sector of the companies.', 'type': 'string',
                       'required': False, 'example': 'Advertising Agencies'},
            'require_non_empty_total_synergies': {'in': 'query',
                                                  'description': 'Flag to require non-empty total synergies in the returned results.',
                                                  'type': 'boolean',
                                                  'required': True, 'example': 'False', 'default': False},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                search_string_company = request.args.get('search', '')
                sector = request.args.get('sector', '')

                require_non_empty_total_synergies = request.args.get('require_non_empty_total_synergies', False)

                orient = request.args.get('orient', 'split')
                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))

                current_app.logger.info(f"{request.url}")

                # Convert the string to a Python boolean
                if require_non_empty_total_synergies in ['true', '1']:
                    require_non_empty_total_synergies = True
                else:
                    require_non_empty_total_synergies = False

                df = CSV_loader.get_transactions(sector, require_non_empty_total_synergies, search_string_company, lite=False)

                # give the first 100 entries of the df
                df = df.head(100)

                if page == 0:
                    #json_str = df.to_json(orient=orient, date_format='iso')

                    result = {
                        'total': len(df),
                        'page': 0,
                        'per_page': len(df),
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)

                else:
                    paginated_df, total = paginate_dataframe(df, page, per_page)

                    result = {
                        'total': total,
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_all_transactions')
    class get_all_transactions(Resource):
        @ns.doc('get_all_transactions')
        @role_required('superuser')
        @api.doc(params={**{
            'search': {'in': 'query', 'description': 'Search by target or buyer name.', 'type': 'string',
                       'required': False, 'example': 'Foo'},
            'sector': {'in': 'query', 'description': 'The sector of the companies.', 'type': 'string',
                       'required': False, 'example': 'Advertising Agencies'},
            'require_non_empty_total_synergies': {'in': 'query',
                                                  'description': 'Flag to require non-empty total synergies in the returned results.',
                                                  'type': 'boolean',
                                                  'required': True, 'example': 'False', 'default': False},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                search_string_company = request.args.get('search', '')
                sector = request.args.get('sector', '')

                require_non_empty_total_synergies = request.args.get('require_non_empty_total_synergies', False)

                orient = request.args.get('orient', 'split')
                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))

                current_app.logger.info(f"{request.url}")

                # Convert the string to a Python boolean
                if require_non_empty_total_synergies in ['true', '1']:
                    require_non_empty_total_synergies = True
                else:
                    require_non_empty_total_synergies = False

                df = CSV_loader.get_transactions(sector, require_non_empty_total_synergies, search_string_company, lite=False)

                if page == 0:
                    #json_str = df.to_json(orient=orient, date_format='iso')

                    result = {
                        'total': len(df),
                        'page': 0,
                        'per_page': len(df),
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)

                else:
                    paginated_df, total = paginate_dataframe(df, page, per_page)

                    result = {
                        'total': total,
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
            
    @ns.route('/get_all_transactions_lite')
    class get_all_transactions_lite(Resource):
        @ns.doc('get_all_transactions_lite')
        @role_required('superuser')
        @api.doc(params={**{
            'search': {'in': 'query', 'description': 'Search by target or buyer name.', 'type': 'string',
                       'required': False, 'example': 'Foo'},
            'sector': {'in': 'query', 'description': 'The sector of the companies.', 'type': 'string',
                       'required': False, 'example': 'Advertising Agencies'},
            'require_non_empty_total_synergies': {'in': 'query',
                                                  'description': 'Flag to require non-empty total synergies in the returned results.',
                                                  'type': 'boolean',
                                                  'required': True, 'example': 'False', 'default': False},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                search_string_company = request.args.get('search', '')
                sector = request.args.get('sector', '')

                require_non_empty_total_synergies = request.args.get('require_non_empty_total_synergies', False)

                orient = request.args.get('orient', 'split')
                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))

                current_app.logger.info(f"{request.url}")

                # Convert the string to a Python boolean
                if require_non_empty_total_synergies in ['true', '1']:
                    require_non_empty_total_synergies = True
                else:
                    require_non_empty_total_synergies = False

                df = CSV_loader.get_transactions(sector, require_non_empty_total_synergies, search_string_company, lite=True)

                if page == 0:
                    #json_str = df.to_json(orient=orient, date_format='iso')

                    result = {
                        'total': len(df),
                        'page': 0,
                        'per_page': len(df),
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)

                else:
                    paginated_df, total = paginate_dataframe(df, page, per_page)

                    result = {
                        'total': total,
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_transactions_by_target')
    class get_transactions_by_target(Resource):
        @ns.doc('get_transactions_by_target')
        @role_required('superuser')
        @api.doc(params={**{
            'target': {'in': 'query', 'description': 'Search up to 12 relevant transactions by target cib_id.', 'type': 'string',
                       'required': False, 'example': 'vestas.com', 'default': 'vestas.com'},
            'sector': {'in': 'query', 'description': 'Search by sector', 'type': 'string',
                       'required': False, 'example': 'Professional Services', 'default': None},
            'subsector_1': {'in': 'query', 'description': 'Search by subsector_1 (and sector)', 'type': 'string',
                       'required': False, 'example': 'Car Rental / Leasing', 'default':None},
            'subsector_2': {'in': 'query', 'description': 'Search by subsector_2 (and subsector_1 and sector)', 'type': 'string',
                       'required': False, 'example': 'N/A', 'default': None},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                target = request.args.get('target', None)
                sector = request.args.get('sector',None)
                subsector_1 = request.args.get('subsector_1',0)
                subsector_2 = request.args.get('subsector_2',0)

                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))
                orient = request.args.get('orient', 'split')
                current_app.logger.info(f"{request.url}")
                df = tr.get_transactions_by_target(target=target, sector=sector, subsector_1=subsector_1,subsector_2=subsector_2,lite=False)

                if page == 0:
                    #json_str = df.to_json(orient=orient, date_format='iso')

                    result = {
                        'total': len(df),
                        'page': 0,
                        'per_page': len(df),
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)

                else:
                    paginated_df, total = paginate_dataframe(df, page, per_page)

                    result = {
                        'total': total,
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
            
    @ns.route('/get_transactions_by_target_lite')
    class get_transactions_by_target_lite(Resource):
        @ns.doc('get_transactions_by_target_lite')
        @role_required('superuser')
        @api.doc(params={**{
            'target': {'in': 'query', 'description': 'Search up to 12 relevant transactions by target cib_id.', 'type': 'string',
                       'required': False, 'example': 'vestas.com', 'default': 'vestas.com'},
            'sector': {'in': 'query', 'description': 'Search by sector', 'type': 'string',
                       'required': False, 'example': 'Professional Services', 'default': None},
            'subsector_1': {'in': 'query', 'description': 'Search by subsector_1 (and sector)', 'type': 'string',
                       'required': False, 'example': 'Car Rental / Leasing', 'default':None},
            'subsector_2': {'in': 'query', 'description': 'Search by subsector_2 (and subsector_1 and sector)', 'type': 'string',
                       'required': False, 'example': 'N/A', 'default': None},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                target = request.args.get('target', None)
                sector = request.args.get('sector',None)
                subsector_1 = request.args.get('subsector_1',0)
                subsector_2 = request.args.get('subsector_2',0)

                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))
                orient = request.args.get('orient', 'split')
                current_app.logger.info(f"{request.url}")
                df = tr.get_transactions_by_target(target=target, sector=sector, subsector_1=subsector_1,subsector_2=subsector_2,lite=True)

                if page == 0:
                    #json_str = df.to_json(orient=orient, date_format='iso')

                    result = {
                        'total': len(df),
                        'page': 0,
                        'per_page': len(df),
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)

                else:
                    paginated_df, total = paginate_dataframe(df, page, per_page)

                    result = {
                        'total': total,
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_all_presentations')
    class get_all_presentations(Resource):
        @ns.doc('get_all_presentations')
        @role_required('superuser')
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')
                current_app.logger.info(f"{request.url}")

                df = db.get_transaction_presentations()

                json_str = df.to_json(orient=orient, date_format='iso')

                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_presentation_pdf')
    class get_presentation_pdf(Resource):
        @ns.doc('get_presentation_pdf')
        @role_required('superuser')
        @api.doc(params={**{
            'transaction_id': {'in': 'query', 'description': 'The ID of the transaction.', 'type': 'string',
                             'required': True, 'example': 'TECF-EU-20120521-01', 'default': 'TECF-EU-20120521-01'},
            'column': {'in': 'query', 'description': 'Data field for which source is needed.', 'type': 'string',
                         'required': False, 'example': 'CostSynergiesAbs', 'default': 'CostSynergiesAbs'},
            'data_type': {'in': 'query', 'description': 'Which data do you need?', 'type': 'string',
                         'required': False,  'enum': [e.name for e in DocProp], 'example': DocProp.DOCUMENT.name, 'default': DocProp.DOCUMENT.name}
        }})
        def get(self):
            try:
                transaction_id = request.args.get('transaction_id', 'QRTEA-US-20170404-01')
                column = request.args.get('column', 'Investor presentation')
                if column == 'PRESENTATION': column = 'Investor presentation' #temp measure to transition from the old system
                data_type = request.args.get('data_type',DocProp.DOCUMENT.name)
                data_type = DocProp[data_type]

                current_app.logger.info(f"{request.url}")

                if data_type.value == 'document':
                    pdf_blob = db.get_presentation_by_id(transaction_id=transaction_id, field=column)
                    if pdf_blob is None:
                        return Response(json.dumps({"error": "Not Found", "message": "Presentation not found."}), status=404,
                                        mimetype='application/json')
                    else:
                        pdf_buffer = BytesIO(pdf_blob)
                        # Serve the PDF using send_file
                        return send_file(pdf_buffer, mimetype='application/pdf', as_attachment=True,
                                         download_name=f'{transaction_id}.pdf')
                elif data_type.value == 'label':
                    label = db.generate_label(transaction_id=transaction_id, field=column)
                    if label != None:
                        return label
                    else:
                        return Response(json.dumps({"error": "Not Found", "message": "Label not found."}), status=404,
                                    mimetype='application/json')
                else:
                    return Response(json.dumps({"error": "Neither label nor document", "message": "Not found."}), status=404,
                                    mimetype='application/json') 
            except Exception as e:
                return log_and_return_error(e)
            
    @ns.route('/get_presentation_references')
    class get_presentation_references(Resource):
        @ns.doc('get_presentation_references')
        @auth.login_required
        @api.expect(api.model('table_model', table_model))
        def post(self):
            try:
                data = request.json
                deal_ids = data.get('deal_ids',[])
                #column_names = data.get('column_names')
                orient = data.get('orient', 'records')

                current_app.logger.info(f"{request.url} :: {data}")

                result = db.generate_label_all(ids=deal_ids)
                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
            
    api.add_namespace(ns)
from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.CSV_connector as csv_loader
from enum import Enum

import json

from src.routes.routes_common import orient_dict

from src.data_platform.Presentation_loader import ResultType
from src.data_platform.Presentation_loader import get_result_by_type



def register_routes_presentation(api, auth):
    ns = api.namespace('presentation', description='Presentation details.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_map_style')
    class get_map_style(Resource):
        @ns.doc('get_map_style')
        @auth.login_required
        @api.doc(params={**{

        },**orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient')

                current_app.logger.info(f"{request.url}")

                CASE_DICT = {
                    1: {"coloring": False, "show_dots": True},
                    2: {"coloring": True, "show_dots": True},
                    3: {"coloring": True, "show_dots": False},
                }

                df = csv_loader.sectors2map
                df['map_type_expanded'] = df['map_type'].map(CASE_DICT)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_yaml_config')
    class get_yaml_config(Resource):
        @ns.doc('get_yaml_config')
        @auth.login_required
        @api.doc(params={**{
            'result_type': {'in': 'query', 'description': 'YAML section (string) or a sector id (number). Depends on what the other parameter is.', 'type': 'string',
                     'required': True, 'enum': [e.name for e in ResultType], 'example': ResultType.TOPLEVEL_YAML_SECTION.name,
                     'default': ResultType.TOPLEVEL_YAML_SECTION.name},
            'param': {'in': 'query', 'description': 'YAML section (string) or a sector id (number). Depends on what the other parameter is.', 'type': 'string',
                     'required': False, 'example': "Allowed_sectors", 'default': "Allowed_sectors"},
        },})
        def get(self):
            try:
                result_type = request.args.get('result_type')
                param = request.args.get('param')

                current_app.logger.info(f"{request.url}")

                result = get_result_by_type(ResultType[result_type], param)

                json_str = json.dumps(result)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)
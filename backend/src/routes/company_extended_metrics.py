import pandas as pd
from flask import request, Response, current_app
from flask_restx import Resource
import json
from flask_restx import fields

import src.data_platform.DB_connector as DB

import src.analysis.company_extended_metrics.population as population
import src.analysis.company_extended_metrics.population_cities as population_cities

import src.analysis.company_extended_metrics.gdp as gdp
import src.analysis.company_extended_metrics.wealth as wealth
import src.analysis.company_extended_metrics.income as income



def register_routes_company_extended_metrics(api, auth):
    ns = api.namespace('company_extended_metrics', description='Extended metrics for companies.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        #return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
        #                mimetype='application/json')
        return Response(json.dumps([]), status=200,
                        mimetype='application/json')
    cib_ids_model = {
        'metric_type': fields.String(
            required=True,
            description='Metric type.',
            example='POPULATION | GDP | WEALTH'
        ),
        'metric_geography': fields.String(
            required=True,
            description='Metric type.',
            example='LOCATION | COUNTRY | CITY'
        ),
        'cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of companiy IDs.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    buyer_targets_model = {
        'metric_type': fields.String(
            required=True,
            description='Metric type.',
            example='POPULATION | GDP | WEALTH'
        ),
        'metric_geography': fields.String(
            required=True,
            description='Metric type.',
            example='LOCATION | COUNTRY | CITY'
        ),
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'target_cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of companiy IDs.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}



    @ns.route('/get_metric_reach')
    class get_metric_reach(Resource):
        @ns.doc('get_metric_reach')
        @auth.login_required
        @api.expect(api.model('cib_ids_model', cib_ids_model))
        def post(self):
            try:
                data = request.json
                metric_type = data.get('metric_type')
                metric_geography = data.get('metric_geography')
                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'records')

                current_app.logger.info(f"{request.url} :: {data}")

                case_dict = {
                    ('POPULATION', 'LOCATION'): lambda cib_ids: population.get_population_reach_locations(cib_ids),
                    ('POPULATION', 'COUNTRY'): lambda cib_ids: population.get_population_reach_countries(cib_ids),
                    ('POPULATION', 'CITY'): lambda cib_ids: population_cities.get_cities_population_reach(cib_ids),
                    ('GDP', "LOCATION"): lambda cib_ids: gdp.get_gdp_reach_locations(cib_ids),
                    ('GDP', 'COUNTRY'): lambda cib_ids: gdp.get_gdp_reach_country(cib_ids),
                    ('WEALTH', 'COUNTRY'): lambda cib_ids: wealth.get_wealth_reach(cib_ids)
                }

                default_df = lambda cib_ids: pd.DataFrame.from_records([{'error': 'Unknown REACH metric requested for: {}'.format(cib_ids)}])

                what_to_do = case_dict.get((metric_type, metric_geography), default_df)

                result = what_to_do(cib_ids)

                column_to_sort_by = result['column_name'].iloc[0]
                result = result.sort_values(by=column_to_sort_by, ascending=False)

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_metric_increase')
    class get_metric_increase(Resource):
        @ns.doc('get_metric_increase')
        @auth.login_required
        @api.expect(api.model('buyer_targets_model', buyer_targets_model))
        def post(self):
            try:
                data = request.json

                metric_type = data.get('metric_type')
                metric_geography = data.get('metric_geography')

                buyer_cib_id = data.get('buyer_cib_id')
                target_cib_ids = data.get('target_cib_ids', [])

                orient = data.get('orient', 'records')
                current_app.logger.info(f"{request.url} :: {data}")

                case_dict = {
                    ('POPULATION', 'LOCATION'): lambda buyer_cib_id, target_cib_ids: population.get_population_increase_locations(buyer_cib_id, target_cib_ids),
                    ('POPULATION', 'COUNTRY'): lambda buyer_cib_id, target_cib_ids: population.get_population_increase_countries(buyer_cib_id, target_cib_ids),
                    ('POPULATION', 'CITY'): lambda buyer_cib_id, target_cib_ids: population_cities.get_population_increase_multiple_targets(buyer_cib_id, target_cib_ids),
                    ('GDP', "LOCATION"): lambda buyer_cib_id, target_cib_ids: gdp.get_gdp_increase_locations(buyer_cib_id, target_cib_ids),
                    ('GDP', 'COUNTRY'): lambda buyer_cib_id, target_cib_ids: gdp.get_gdp_increase_country(buyer_cib_id, target_cib_ids),
                    ('WEALTH', 'COUNTRY'): lambda buyer_cib_id, target_cib_ids: wealth.get_wealth_increase_multiple_targets(buyer_cib_id, target_cib_ids)
                }

                default_df = lambda cib_ids: pd.DataFrame.from_records(
                    [{'error': 'Unknown INCREASE metric requested for: {}'.format(cib_ids)}])

                what_to_do = case_dict.get((metric_type, metric_geography), default_df)

                result = what_to_do(buyer_cib_id, target_cib_ids)

                json_str = result.to_json(orient=orient, date_format='iso')

                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    buyer_targets_country_company_metrics = {
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'country_code': fields.Integer(
            required=True,
            description='Country code represented as an integer.',
            example=63
        ),
        'target_cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of target companies.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    @ns.route('/get_incremental_income')
    class get_incremental_income(Resource):
        @ns.doc('get_incremental_income')
        @auth.login_required
        @api.expect(api.model('BuyerTargetsCountryMetrics', buyer_targets_country_company_metrics))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                country_code = data.get('country_code', 63)
                target_cib_ids = data.get('target_cib_ids', [])
                orient = data.get('orient', 'records')

                current_app.logger.info(f"{request.url} :: {data}")

                if country_code == 0:
                    result = pd.DataFrame()
                else:
                    result = income.get_income_increase_locations(buyer_cib_id, target_cib_ids, country_code)

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    buyer_targets_function_country_company_metrics = {
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'country_code': fields.Integer(
            required=True,
            description='Country code represented as an integer.',
            example=0
        ),
        'function_code': fields.Integer(
            required=True,
            description='Function code represented as an integer.',
            example=0
        ),
        'sector_id': fields.Integer(
            required=True,
            description='Sector ID, needed for overlap ',
            example=0
        ),
        'target_cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of target companies.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    @ns.route('/get_company_overlaps')
    class get_company_overlaps(Resource):
        @ns.doc('get_company_overlaps')
        @auth.login_required
        @api.expect(api.model('BuyerTargetsFunctionCountryMetrics', buyer_targets_function_country_company_metrics))
        def post(self):
            try:
                data = request.json

                # Extract parameters from the JSON body
                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                country_code = data.get('country_code', -1)
                function_code = data.get('function_code', -1)
                sector_id = data.get('sector_id', 0)
                target_cib_ids = data.get('target_cib_ids', [])
                orient = data.get('orient', 'records')

                current_app.logger.info(f"{request.url} :: {data}")

                try:
                    result = DB.get_overlaps_multiple_targets(buyer_cib_id, target_cib_ids, country_code, function_code)
                except Exception as e:
                    current_app.logger.error(f"Error getting overlaps: {e}")
                    return log_and_return_error(e)

                if sector_id != 0:
                    overlap_column = DB.get_sector_default_overlap_2(sector_id)
                else:
                    overlap_column = "overlap_25"

                result['column_name'] = overlap_column
                result['column_label'] = f"Location overlap at {overlap_column.split('_')[1]}km distance"

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)
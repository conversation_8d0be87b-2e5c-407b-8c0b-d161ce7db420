from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
import src.data_platform.CSV_connector as CSV_loader
import src.utils.paths.csv_paths as csv_paths
import pandas as pd
import json
import os


from enum import Enum

class Role(Enum):
    DEFAULT = (
        True,  # allow_transactions
        True,  # allow_snapshot_view
        True,  # allow_target_search
        True   # allow_antitrust
    )

    def __init__(
        self,
        allow_transactions: bool,
        allow_snapshot_view: bool,
        allow_target_search: bool,
        allow_antitrust: bool,
    ):
        self.allow_transactions = allow_transactions
        self.allow_snapshot_view = allow_snapshot_view
        self.allow_target_search = allow_target_search
        self.allow_antitrust = allow_antitrust

    def to_dict(self) -> dict:
        return {
            "allow_transactions": self.allow_transactions,
            "allow_snapshot_view": self.allow_snapshot_view,
            "allow_target_search": self.allow_target_search,
            "allow_antitrust": self.allow_antitrust,
        }

whitelist_df = pd.read_csv(csv_paths.WHITELIST, sep=";")



# Function to check if an email belongs to any domains in the list
def check_email_domain(input_email):
    # Extract the domain from the input email
    input_domain = input_email.split('@')[-1]

    # Loop through the 'URL' column in the dataframe to check if the domain matches
    for index, row in whitelist_df.iterrows():
        domain = row['URL'].split('/')[0]
        if input_domain == domain:
            return row

    return False

def check_email_roles(input_email):
    return Role.DEFAULT


def read_deployment_info(file_path='deployment-info.txt'):
    deployment_info = {}
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"{file_path} does not exist.")

    with open(file_path, 'r') as file:
        for line in file:
            # Ignore empty lines
            line = line.strip()
            if line:
                # Split key=value pairs
                key, value = line.split('=', 1)
                deployment_info[key] = value

    return deployment_info


def register_routes_system(api, auth):
    ns = api.namespace('system', description='System info.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/deployment_info')
    class deployment_info(Resource):
        @ns.doc('deployment_info')
        @auth.login_required
        @api.doc(params={**{
        },})
        def get(self):
            try:
                current_app.logger.info(f"{request.url}")

                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
                deployment_info_file = os.path.join(project_root, "../deploymentinfo.txt")
                #print(deployment_info_file)
                result = read_deployment_info(deployment_info_file)

                json_str = json.dumps(result, indent=2)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/allowed_email')
    class allowed_email(Resource):
        @ns.doc('allowed_email')
        @auth.login_required
        @api.doc(params={**{
            'email': {'in': 'query', 'description': 'The email address to check.', 'type': 'string',
                             'required': True, 'example': '<EMAIL>'},
        }, })
        def get(self):
            try:
                email = request.args.get('email')

                current_app.logger.info(f"{request.url}")

                # Check if the email domain is in the whitelist
                result = check_email_domain(email)

                if result is False:
                    to_ret = {
                        'allowed': False,
                        'email': email,
                        'reason': 'Domain not in whitelist'
                    }
                else:
                    to_ret = {
                        'allowed': True,
                        'email': email,
                        'company': result['Company'],
                        'url': result['URL']
                    }

                json_str = json.dumps(to_ret, indent=2)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/check_roles')
    class check_roles(Resource):
        @ns.doc('check_roles')
        @auth.login_required
        @api.doc(params={**{
            'email': {'in': 'query', 'description': 'The email address to check.', 'type': 'string',
                             'required': True, 'example': '<EMAIL>'},
        }, })
        def get(self):
            try:
                email = request.args.get('email')

                current_app.logger.info(f"{request.url}")

                # Check if the email domain is in the whitelist
                result = check_email_roles(email).to_dict()

                json_str = json.dumps(result, indent=2)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)


import numpy as np
import pandas as pd
from flask import request, Response, current_app
from flask_restx import Resource
from src.analysis.company_specific_info import it_consulting_suppliers, retail_cars
import src.analysis.company_specific_info.real_estate as real_estate
import src.analysis.company_specific_info.wealth_management as wealth_management
import src.analysis.company_specific_info.strip_profiles.strip_profiles as strip_profiles

import src.analysis.company_specific_info.asset_management as asset_management

import src.analysis.company_specific_info.ecommerce_brands as ecommerce_brands
import src.analysis.company_specific_info.hospitals as hospitals
import src.analysis.company_specific_info.hospitals_specializations as hospitals_specializations
import src.analysis.company_specific_info.banks as banks
import src.analysis.company_specific_info.insurance_broker as insurance_broker
import src.data_platform.DB_connector as DB

from enum import Enum
import json
from flask_restx import fields
from src.routes.routes_common import orient_dict


class REAL_ESTATE_REGION(Enum):
    CITY = "CITY"
    STATE = "STATE"

def register_routes_company_specific_info(api, auth):
    ns = api.namespace('company_specific_info', description='Company specific info.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_real_estate_portfolio')
    class get_real_estate_portfolio(Resource):
        @ns.doc('get_real_estate_portfolio')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'aroundtown.de', 'default': 'aroundtown.de'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': False, 'example': 'tag-ag.com', 'default': 'tag-ag.com'},
            'region_type': {'in': 'query', 'description': 'Region type NOT USED FOR NOW', 'type': 'string',
                       'required': True, 'enum': [e.name for e in REAL_ESTATE_REGION],
                       'example': REAL_ESTATE_REGION.CITY.name,
                       'default': REAL_ESTATE_REGION.CITY.name},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')
                region_type = request.args.get('region_type')
                orient = request.args.get('orient')

                current_app.logger.info(f"{request.url}")

                df = real_estate.get_by_buyer_and_target(acquiror_cib_id,
                                                         target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_real_estate_portfolio_notes')
    class get_real_estate_portfolio_notes(Resource):
        @ns.doc('get_real_estate_portfolio_notes')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'aroundtown.de', 'default': 'aroundtown.de'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': False, 'example': 'tag-ag.com', 'default': 'tag-ag.com'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')
                orient = request.args.get('orient')

                current_app.logger.info(f"{request.url}")

                df = real_estate.get_notes(acquiror_cib_id, target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/has_real_estate_data')
    class has_real_estate_data(Resource):
        @ns.doc('has_real_estate_data')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company to check whether it has real-estate portfolio.', 'type': 'string',
                             'required': True, 'example': 'vonovia.com', 'default': 'vonovia.com'},
        },})
        def get(self):
            try:
                cib_id = request.args.get('cib_id')

                current_app.logger.info(f"{request.url}")

                x= bool(real_estate.has_real_estate_data(cib_id))
                print(x)

                result = {"cib_id": cib_id, "has_real_estate_data": x}

                json_str = json.dumps(result)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    companies_model = {
        'cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of Wealth Management companies.',
            example=['25financial.com', 'acadviser.com', 'almanackip.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    wm_model = {
        'data_type': fields.String(
            required=True,
            description='The type of data to retrieve.',
            example='AUM | AUM_client | num_clients',
            default='AUM | AUM_client | num_clients',
        ),
        **companies_model,
    }

    @ns.route('/get_wealth_management_data')
    class get_wealth_management_data(Resource):
        @ns.doc('get_wealth_management_data')
        @auth.login_required
        @api.expect(api.model('WMModel', wm_model))
        def post(self):
            try:
                data = request.json

                data_type = data.get('data_type', 'AUM')
                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')

                cases = {
                    "AUM": "AUM ($bn)",
                    "AUM_client": "Average AUM / client",
                    "num_clients": "No. of clients",
                }

                current_app.logger.info(f"{request.url}")

                df = wealth_management.get_data(cib_ids)

                df['column_label'] = cases.get(data_type, "unknown")
                df['column_name'] = data_type
                df[data_type] = df[cases[data_type]]

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_asset_management_data')
    class get_asset_management_data(Resource):
        @ns.doc('get_asset_management_data')
        @auth.login_required
        @api.expect(api.model('CompaniesModel', companies_model))
        def post(self):
            try:
                data = request.json

                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')


                current_app.logger.info(f"{request.url}")

                df = asset_management.get_data(cib_ids)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_insurance_broker_data')
    class get_insurance_broker_data(Resource):
        @ns.doc('get_insurance_broker_data')
        @auth.login_required
        @api.expect(api.model('CompaniesModel', companies_model))
        def post(self):
            try:
                data = request.json

                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')


                current_app.logger.info(f"{request.url}")

                df = insurance_broker.get_data(cib_ids)
                df['column_label'] = "Employees (#)"
                df['column_name'] = "employees"

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    cars_model = ns.model('CarsModel', {
        'acquiror': fields.String(required=True, description='The acquiror URL for cars', example='alphartis.com', default='alphartis.com'),
        'target': fields.String(required=True, description='The target URL for cars', example='amag.ch', default='amag.ch')
    })

    @ns.route('/get_cars_data')
    class get_cars_data(Resource):
        @ns.doc('get_cars_data')
        @auth.login_required
        @ns.expect(cars_model)
        def post(self):
            try:
                acquiror: str = request.json['acquiror']
                target: str = request.json['target']

                current_app.logger.info(f"{request.url}")

                brands_oems: dict = retail_cars.get_by_acquiror_target(acquiror_url=acquiror, target_url=target)
                return brands_oems
            except Exception as e:
                return log_and_return_error(e)
    
    
    it_consulting_suppliers_model = ns.model('ITConsultingSuppliersModel', {
        'acquiror': fields.String(required=True, description='The acquiror company name.', example='12systems', default='12systems'),
        'target': fields.String(required=True, description='The target company name.', example='abilis Group', default='abilis Group')
    })

    @ns.route('/get_it_consulting_suppliers_data')
    class get_it_consulting_suppliers_data(Resource):
        @ns.doc('get_it_consulting_suppliers_data')
        @auth.login_required
        @ns.expect(it_consulting_suppliers_model)
        def post(self):
            try:
                acquiror: str = request.json['acquiror']
                target: str = request.json['target']

                current_app.logger.info(f"{request.url}")

                suppliers: dict = it_consulting_suppliers.get_by_acquiror_target(acquiror, target)
                return suppliers
            except Exception as e:
                return log_and_return_error(e)


    ecommerce_model = ns.model('ECommerceModel', {
        'acquiror_url': fields.String(required=True, description='The acquiror URL.', example='corporate.zalando.com'),
        'target_url': fields.String(required=True, description='The target URL.', example='aboutyou.de'),
        'round_to_thousands': fields.Boolean(required=False, description='Round to thousands.', example=True, default=True)
    })

    @ns.route('/get_ecommerce_data')
    class get_ecommerce_data(Resource):
        @ns.doc('get_ecommerce_data')
        @auth.login_required
        @ns.expect(ecommerce_model)
        def post(self):
            try:
                acquiror_url: str = request.json['acquiror_url']
                target_url: str = request.json['target_url']
                round_to_thousands: bool = request.json['round_to_thousands']

                current_app.logger.info(f"{request.url}")

                ecommerce_data: dict = ecommerce_brands.get_by_acquiror_target(
                    acquiror_url=acquiror_url,
                    target_url=target_url,
                    round_to_thousands=round_to_thousands,
                )
                return ecommerce_data
            except Exception as e:
                return log_and_return_error(e)


    hospitals_model = ns.model('HospitalModel', {
        'acquiror_url': fields.String(required=True, description='The acquiror URL.', example='ameos.de'),
        'target_url': fields.String(required=True, description='The target URL.', example='sana.de'),
    })

    @ns.route('/get_hospitals_data')
    class get_hospitals_data(Resource):
        @ns.doc('get_hospitals_data')
        @auth.login_required
        @ns.expect(hospitals_model)
        def post(self):
            try:
                acquiror_url: str = request.json['acquiror_url']
                target_url: str = request.json['target_url']

                current_app.logger.info(f"{request.url}")

                hospitals_data: dict = hospitals.get_by_acquiror_target(
                    acquiror_url=acquiror_url,
                    target_url=target_url,
                )
                return {"hospitals": hospitals_data}
            except Exception as e:
                return log_and_return_error(e)

    banks_model = ns.model('BankModel', {
        'acquiror_url': fields.String(required=True, description='The acquiror URL.', example='temp.rbcbrewindolphin.com'),
        'target_url': fields.String(required=True, description='The target URL.', example='temp.evelynpartnersgroup.com'),
    })

    @ns.route('/get_banks_data')
    class get_banks_data(Resource):
        @ns.doc('get_banks_data')
        @auth.login_required
        @ns.expect(banks_model)
        def post(self):
            try:
                acquiror_url: str = request.json['acquiror_url']
                target_url: str = request.json['target_url']

                current_app.logger.info(f"{request.url}")

                data: dict = banks.get_by_acquiror_target(
                    acquiror_url=acquiror_url,
                    target_url=target_url,
                )
                return {"banks": data}
            except Exception as e:
                return log_and_return_error(e)

    hospitals_specializations_model = ns.model('HospitalSpecializationModel', {
        'acquiror_url': fields.String(required=True, description='The acquiror URL.', example='median-kliniken.de'),
        'target_url': fields.String(required=True, description='The target URL.', example='mediclin.de'),
    })

    @ns.route('/get_hospitals_specializations_data')
    class get_hospitals_specializations_data(Resource):
        @ns.doc('get_hospitals_specializations_data')
        @auth.login_required
        @ns.expect(hospitals_specializations_model)
        def post(self):
            try:
                acquiror_url: str = request.json['acquiror_url']
                target_url: str = request.json['target_url']

                current_app.logger.info(f"{request.url}")

                data: dict = hospitals_specializations.get_by_acquiror_target(
                    acquiror_url=acquiror_url,
                    target_url=target_url,
                )
                return {"hospitals_specializations": data}
            except Exception as e:
                return log_and_return_error(e)

    example_fields_list = [
        'company_name',
        'product_summary',
        'product_summary_source_complete',
        'client_summary',
        'client_focus_source_complete',
        'firm_years_summary',
        'firm_years_summary_source_complete',
        'locations_text',
        'locations_text_source_complete',
        'hq',
        'hq_source_complete',
        'AUM',
        'aum_source_complete',
        'AUM_detail',
        'aum_detail_source_complete',
        'foundingyear_HQ_combined',
        'foundingyear_HQ_combined_source_complete',
        'total_employees',
        'total_employees_source_complete',
        'total_employees_cagr',
        'total_employees_cagr_source_complete',
        'revenue',
        'revenue_source_complete',
        'operating_profit',
        'operating_profit_source_complete',
        'operating_profit_cagr',
        'operating_profit_cagr_source_complete',
        'ownership_type',
        'ownership_type_source_complete',
        'ownership_detail',
        'ownership_detail_source_complete'
    ]


    strip_profile_model = {
        'data_type': fields.List(
            fields.String,
            required=True,
            description='The type of data to retrieve.',
            example=example_fields_list
        ),
        **companies_model,
    }

    @ns.route('/get_strip_profile_data')
    class get_strip_profile_data(Resource):
        @ns.doc('get_strip_profile_data')
        @auth.login_required
        @api.expect(api.model('StripProfile', strip_profile_model))
        def post(self):
            try:
                data = request.json

                columns = data.get('data_type', 'AUM')
                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')


                current_app.logger.info(f"{request.url}")

                df = strip_profiles.get_loader().get_columns_for_cib_ids(cib_ids, columns)

                if "company_name" in columns:
                    x = DB.get_names_by_cib_ids(cib_ids)
                    df.drop(columns=['company_name'], inplace=True, errors='ignore')
                    df = df.merge(x[['cib_id', 'company_name']], on='cib_id', how='left')

                df = df.replace(r'(?i)^unknown$', 'N/A', regex=True)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    modified_strip_profile_model = {
        'field': fields.List(
            fields.String,
            required=True,
            description='The field of data to retrieve.',
            example=' | '.join(example_fields_list),
        ),
        'type': fields.List(
            fields.String,
            required=True,
            description='The type of data to retrieve.',
            example='numeric | str'),
        **companies_model,
    }

    @ns.route('/get_strip_profile_data_simplified')
    class get_strip_profile_data_simplified(Resource):
        @ns.doc('get_strip_profile_data_simplified')
        @auth.login_required
        @api.expect(api.model('StripProfileSimplified', modified_strip_profile_model))
        def post(self):
            try:
                data = request.json

                field = data.get('field', 'company_name')
                type = data.get('type', 'str')
                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                source_col = f"{field}_source_complete"
                df = strip_profiles.get_loader().get_columns_for_cib_ids(cib_ids, [field, source_col])

                #print(df)

                if source_col not in df.columns:
                    df[source_col] = np.nan  # or '' or any default value you prefer

                df['column_label'] = field
                df['column_name'] = field
                df['column_source'] = source_col

                #print(df)

                if type == 'numeric':
                    df[field] = pd.to_numeric(df[field].astype(str).str.replace(',', '.').str.replace(' ', ''),
                                              errors='coerce')

                df = df.replace(r'(?i)^unknown$', 'N/A', regex=True)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_cib_ids_with_strip_profiles')
    class get_cib_ids_with_strip_profiles(Resource):
        @ns.doc('get_cib_ids_with_strip_profiles')
        @auth.login_required
        @api.expect(api.model('CompaniesModel', companies_model))
        def post(self):
            try:
                data = request.json

                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                ids = strip_profiles.get_available_cib_ids_with_strip_profiles(cib_ids)

                json_str = json.dumps(ids, indent=2)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_map_for_cib_ids')
    class get_map_for_cib_ids(Resource):
        @ns.doc('get_map_for_cib_ids')
        @auth.login_required
        @api.expect(api.model('CompaniesModel', companies_model))
        def post(self):
            try:
                data = request.json

                cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = strip_profiles.get_strip_profiles_map(cib_ids)
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)
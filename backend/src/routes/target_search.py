from typing import Any
from flask import request, Response, current_app
from flask_restx import Resource
from sqlalchemy import False_

import src.data_platform.Clickhouse_connector as cc
import src.data_platform.DB_connector as db
from enum import Enum
from typing import Optional, Tuple
import src.utils.paths.csv_paths as csv_paths
import pandas as pd


import json

from src.routes.routes_common import orient_dict
from src.analysis.company_specific_info.strip_profiles import strip_profiles
from src.analysis.company_specific_info.strip_profiles.panel_factory import create_panel_for_sector


class Strategy(Enum):
    CONSOLIDATE = "Consolidate existing footprint"
    EXPAND_ADJACENT = "Expand footprint to adjacent area"
    EXPAND_NEW_COUNTRIES = "Expand footprint in new countries"


class Size(Enum):
    TUCK_IN = "Tuck-in (<=10%)"
    BOLT_ON = "Bolt-on (<=25%)"
    ACQUISITION = "Acquisition (<=75%)"
    LARGE_ACQUISITION = "Large acquisition (<=200%)"
    TOO_LARGE = "Transformational (>200%)"
    ANY = "Any size"

    @property
    def size_bounds(self) -> <PERSON><PERSON>[float, Optional[float]]:
        return {
            Size.TUCK_IN: (0.0, 0.1),
            Size.BOLT_ON: (0.1, 0.25),
            Size.ACQUISITION: (0.25, 0.75),
            Size.LARGE_ACQUISITION: (0.75, 2.0),
            Size.TOO_LARGE: (2.0, 1000000.0),
            Size.ANY: (0.0, 100000.0)
        }[self]


### Old size definition
# class Size(Enum):
#     TUCK_IN = "Tuck-in (<=10%)"
#     BOLT_ON = "Bolt-on (<=25%)"
#     ACQUISITION = "Acquisition (<=75%)"
#     LARGE_ACQUISITION = "Large acquisition (<=200%)"
#     TOO_LARGE = "Transformational (>200%)"
#     ANY = "Any size"

#     @property
#     def sql_condition(self):
#         size_conditions = {
#             Size.TUCK_IN: "AND cm.relative_size <= 0.1",
#             Size.BOLT_ON: "AND cm.relative_size <= 0.25 AND cm.relative_size > 0.1",
#             Size.ACQUISITION: "AND cm.relative_size <= 0.75 AND cm.relative_size > 0.25",
#             Size.LARGE_ACQUISITION: "AND cm.relative_size <= 2 AND cm.relative_size > 0.75",
#             Size.TOO_LARGE: "AND cm.relative_size > 2",
#             Size.ANY: ""
#         }
#         return size_conditions[self]

### Not needed for now 
# class Region:
#     def __init__(self, region):
#         self.region = region

#     @property
#     def sql_condition(self):
#         if self.region == "-":
#             return ""
#         return f"AND ma.area_level_1 = '{self.region}'"


def register_routes_target_search(api, auth):
    ns = api.namespace('target_search', description='Target search queries.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_targets')
    class get_targets(Resource):
        @ns.doc('get_targets')
        @auth.login_required
        @api.doc(params={**{
            'strategy': {'in': 'query', 'description': 'Strategy type.', 'type': 'string',
                        'required': True,  'enum': [e.name for e in Strategy], 'example': Strategy.EXPAND_NEW_COUNTRIES.name, 'default': Strategy.EXPAND_NEW_COUNTRIES.name},
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID of the acquiror.', 'type': 'string',
                                'required': True, 'example': 'temp.oldnationalbank.com', 'default': 'temp.oldnationalbank.com'},
            'continent_id': {
                'in': 'query',
                'description': (
                    'The ID of the continent. Must be provided first:\n'
                    '- If continent_id = 0 the search is global.\n'
                    '- If continent_id != 0 setting country_id to 0 makes the search continent-specific.'
                ),
                'type': 'integer', 'required': True, 'example': 4, 'default': 4
            },
            'country_id': {
                'in': 'query',
                'description': (
                    'The ID of the country. Must match the continent or be 0, unless continent_id = 0:\n'
                    '- If continent_id = 0 country_id is not evaluated.\n'
                    '- If continent_id != 0, country_id = 0 makes the search continent-specific.\n'
                    '- If continent_id != 0 and country_id != 0, the search is:\n'
                    '  - Country-specific if `region = \'-\'`\n'
                    '  - Region-specific if `region` is a valid region name.'
                ),
                'type': 'integer', 'required': True, 'example': 185, 'default': 185
            },
            'sector_id': {'in': 'query', 'description': 'The ID of the sector.', 'type': 'integer',
                        'required': True, 'example': 7, 'default': 7},
            'size': {'in': 'query', 'description': 'Target size.', 'type': 'string',
                    'required': True, 'enum': [e.name for e in Size], 'example': Size.BOLT_ON.name, 'default': Size.BOLT_ON.name},
            'region': {
                'in': 'query',
                'description': (
                    'The name of the region (e.g., "New York"). Interpreted only when continent_id and country_id are both non-zero.\n'
                    '\'-\' is default selection, provide valid region name to use region-specific search.'
                ),
                'type': 'string', 'required': False, 'example': 'New York', 'default': '-'
            }
        }, **orient_dict})
        def get(self):
            try:
                strategy = request.args.get('strategy')
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                continent_id = request.args.get('continent_id')
                country_id = request.args.get('country_id')
                sector_id = request.args.get('sector_id')
                size_string = request.args.get('size')
                region_name = request.args.get('region', '-')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                # size_enum = Size[size_string]
                # size_condition = size_enum.sql_condition
                size_enum = Size[size_string]
                lower_limit, upper_limit = size_enum.size_bounds

                case_dict = {
                    Strategy.CONSOLIDATE: lambda: cc.consolidate(acquiror_cib_id, sector_id, continent_id, country_id, lower_limit, upper_limit, region_name),
                    Strategy.EXPAND_ADJACENT: lambda: cc.expand_adjacent(acquiror_cib_id, sector_id, lower_limit, upper_limit),
                    Strategy.EXPAND_NEW_COUNTRIES: lambda: cc.expand_new_countries(acquiror_cib_id, sector_id, continent_id, country_id, lower_limit, upper_limit, region_name),
                }

                df = case_dict[Strategy[strategy]]()

                if df.empty:
                    return Response("{}", mimetype='application/json')

                company_names = db.get_names_by_cib_ids(df['target_cib_id'].tolist())
                result = df.merge(company_names, left_on='target_cib_id', right_on='cib_id', how='inner')


                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_targets_no_buyer')
    class get_targets_no_buyer(Resource):
        @ns.doc('get_targets_no_buyer')
        @auth.login_required
        @api.doc(params={**{
            'continent_id': {
                'in': 'query',
                'description': (
                    'The ID of the continent. Must be provided first:\n'
                    '- If continent_id = 0 the search is global.\n'
                    '- If continent_id != 0 setting country_id to 0 makes the search continent-specific.'
                ),
                'type': 'integer', 'required': True, 'example': 4, 'default': 4
            },
            'country_id': {
                'in': 'query',
                'description': (
                    'The ID of the country. Must match the continent or be 0, unless continent_id = 0:\n'
                    '- If continent_id = 0 country_id is not evaluated.\n'
                    '- If continent_id != 0, country_id = 0 makes the search continent-specific.\n'
                    '- If continent_id != 0 and country_id != 0, the search is:\n'
                    '  - Country-specific if `region = \'-\'`\n'
                    '  - Region-specific if `region` is a valid region name.'
                ),
                'type': 'integer', 'required': True, 'example': 185, 'default': 185
            },
            'sector_id': {'in': 'query', 'description': 'The ID of the sector.', 'type': 'integer',
                        'required': True, 'example': 7, 'default': 7},
            'region': {
                'in': 'query',
                'description': (
                    'The name of the region (e.g., "New York"). Interpreted only when continent_id and country_id are both non-zero.\n'
                    '\'-\' is default selection, provide valid region name to use region-specific search.'
                ),
                'type': 'string', 'required': False, 'example': 'New York', 'default': '-'
            },
            'anonymized_companies': {'in': 'query', 'description': 'Whether to obfuscate company names.', 'type': 'boolean',
                       'required': False, 'example': False, 'default': False}
        }, **orient_dict})
        def get(self):
            try:
                continent_id = request.args.get('continent_id')
                country_id = request.args.get('country_id')
                sector_id = request.args.get('sector_id')
                region_name = request.args.get('region', '-')
                orient = request.args.get('orient', 'split')
                anonymized_companies_raw = request.args.get('anonymized_companies', "false")
                anonymized_companies = True if anonymized_companies_raw == "true" else False


                current_app.logger.info(f"{request.url}")

                result = db.target_search_no_buyer(continent_id, country_id, sector_id, region_name if region_name != '-' else None)
                # print(result)
                if anonymized_companies:
                    result['company_name'] = ['Company {}'.format(i + 1) for i in range(len(result))]

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_allowed_sectors')
    class get_allowed_sectors(Resource):
        @ns.doc('get_allowed_sectors')
        @auth.login_required
        @api.doc(params={**{
        }})
        def get(self):
            try:

                # 41 WM / brokerage
                # 34 Retailing - Food
                # 7, - Banks...
                # 32 - Prof Services
                # 97 - Alternative asset management
                # 77 - Retailing auto

                result = [41, 7, 34, 32, 97, 77, 21, 123, 18, 124, 22, 95, 40, 57, 130, 24, 17, 20, 23, 143, 36, 142, 144, 6,
                          4, 84, 86, 85, 115, 157, 31, 165, 25]

                

                return Response(json.dumps(result), mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_cib_overview_panel')
    class get_cib_overview_panel(Resource):
        @ns.doc('get_cib_overview_panel')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code for the company.', 'type': 'string',
                             'required': False, 'example': 'acconsis.de', 'default': 'acconsis.de'},
        }, **orient_dict})
        def get(self):
            try:
                orient: str = request.args.get('orient', 'split')
                cib_id: str = request.args.get('cib_id', 'vestas.com')

                current_app.logger.info(f"{request.url}")


                df_reindexed = strip_profiles.get_loader().get_columns_for_cib_ids([cib_id])

                if df_reindexed.empty:
                    json_str = pd.DataFrame().to_json(orient=orient, date_format='iso')
                    return Response(json_str, mimetype='application/json')
                
                data: dict[str, Any] = df_reindexed.iloc[0].to_dict()

                try:
                    with db.engine.connect() as conn:
                        n_locations: int = db.get_locations_count(conn, cib_id=cib_id)
                        _companies= db.get_multiple_cib_ids([cib_id])
                        company: pd.DataFrame = _companies[_companies["cib_id"] == cib_id]
                        company_name: str = company["company_name"].values[0]
                        company_sectors = company["sectors"].values[0][-1]

                        # print cib_id and its sector
                        print(f"cib_id: {cib_id}, sectors: {company_sectors}")

                        source: str = f"Company website ({company['last_change_date'].values[0]})" if company["last_change_date"].values[0] else ""
                except Exception as e:
                    current_app.logger.error(f"Error getting company name and location count: {e}")
                    n_locations = -1
                    company_name = cib_id
                    source = ""



                df_result: pd.DataFrame = create_panel_for_sector([company_sectors]).format_row_for_target_search(data=data, n_locations=n_locations, locations_source=source, company_name=company_name)

                json_str = df_result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)
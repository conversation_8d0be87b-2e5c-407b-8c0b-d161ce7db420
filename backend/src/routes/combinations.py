from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db

import src.analysis.country_risk as country_risk
import src.analysis.country_salaries as country_income
import src.analysis.overlaps as overlaps

from flask_restx import fields

import json

from src.routes.routes_common import orient_dict

#import src.analysis.synergy_score.generic_synergy_score as synergy_score

import src.analysis.synergy_score.factory.SynergyScoreFactory as SynergyScoreFactory


def register_routes_combinations(api, auth):
    ns = api.namespace('combinations', description='Company combination data.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_overlaps')
    class get_overlaps(Resource):
        @ns.doc('get_overlaps')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com'},
            'function_id': {'in': 'query', 'description': 'The ID of the function.', 'type': 'string',
                           'required': False, 'example': 'TOTAL'},
            'country_id': {'in': 'query', 'description': 'The ID of the function.', 'type': 'string',
                            'required': False, 'example': 'TOTAL'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')
                function_id = request.args.get('function_id', 0)
                country_id = request.args.get('country_id', 0)
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                df = db.get_overlaps(acquiror_cib_id, target_cib_id, function_id, country_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_available_country_combinations')
    class get_available_country_combinations(Resource):
        @ns.doc('get_available_country_combinations')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_available_country_combinations(acquiror_cib_id, target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    def get_common_sectors(acquiror_sector, target_sector, case_dict):
        # Find common sectors in both lists and the dict keys
        return list(set(acquiror_sector) & set(target_sector) & set(case_dict))

    import src.data_platform.DB_connector as DB


    @ns.route('/get_synergy_score')
    class get_synergy_score(Resource):
        @ns.doc('get_synergy_score')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'mediclin.de', 'default': 'mediclin.de'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'median-kliniken.de', 'default': 'median-kliniken.de'},
        }, })
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')

                current_app.logger.info(f"{request.url}")

                with db.engine.connect() as conn:
                    target_sector = db.get_by_cib_id(target_cib_id, engine_or_conn=conn)['sectors'].tolist()[0]
                    print(target_sector)
                    result = SynergyScoreFactory.sector_id_2_synergy_score(conn, target_sector).get_synergy_score(acquiror_cib_id, target_cib_id)

                json_str = json.dumps(result)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_functional_combinations')
    class get_functional_combinations(Resource):
        @ns.doc('get_functional_combinations')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_functional_combinations(acquiror_cib_id, target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_functional_combinations_with_countries')
    class get_functional_combinations_with_countries(Resource):
        @ns.doc('get_functional_combinations_with_countries')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_functional_combinations_with_countries(acquiror_cib_id, target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)







    @ns.route('/get_country_risk')
    class get_country_risk(Resource):
        @ns.doc('get_country_risk')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com'},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = country_risk.get_country_risk(acquiror_cib_id, target_cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_income_by_function')
    class get_income_by_function(Resource):
        @ns.doc('get_income_by_function')
        @auth.login_required
        @api.doc(params={**{
            'acquiror_cib_id': {'in': 'query', 'description': 'The ID code of the acquiror.', 'type': 'string',
                             'required': True, 'example': 'vestas.com', 'default': 'vestas.com'},
            'target_cib_id': {'in': 'query', 'description': 'The ID code of the target.', 'type': 'string',
                           'required': True, 'example': 'nordex-online.com', 'default': 'nordex-online.com'},
            'function_id': {'in': 'query', 'description': 'The ID of the function.', 'type': 'number', 'default': 0},
        }, **orient_dict})
        def get(self):
            try:
                acquiror_cib_id = request.args.get('acquiror_cib_id')
                target_cib_id = request.args.get('target_cib_id')
                function_id = request.args.get('function_id')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = country_income.get_country_salaries(acquiror_cib_id, target_cib_id, function_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)




    buyer_targets_company_metrics = {
        'buyer_cib_id': fields.String(
            required=True,
            description='CIB ID of the buyer.',
            example='valueretail.com'
        ),
        'target_cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of target companies.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    @ns.route('/get_country_overlaps')
    class get_country_overlaps(Resource):
        @ns.doc('get_country_overlaps')
        @auth.login_required
        @api.expect(api.model('BuyerTargetsMetrics', buyer_targets_company_metrics))
        def post(self):
            try:
                data = request.json

                buyer_cib_id = data.get('buyer_cib_id', 'valueretail.com')
                target_cib_ids = data.get('target_cib_ids', [])
                orient = data.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                df = overlaps.get_country_intersection(buyer_cib_id, target_cib_ids)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)
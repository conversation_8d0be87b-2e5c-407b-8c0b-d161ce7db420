import pandas as pd
from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
import src.analysis.whitespot as ws
import src.data_platform.CSV_connector as CSV_loader
from src.routes.routes_common import orient_dict, paginate_dataframe
from enum import Enum
import json
import src.utils.gis_utils as gis_utils

import geojson


class REGION_HEATMAP_DATA(Enum):
    LABEL = "LABEL"
    CATEGORY = "CATEGORY"


def register_routes_gis(api, auth):
    ns = api.namespace('gis', description='GIS (Geographic Information System) functions.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')


    @ns.route('/get_locations_within_range')
    class get_locations_within_range(Resource):
        @ns.doc('get_locations_within_range')
        @auth.login_required
        @api.doc(params={**{
            'lat': {'in': 'query', 'description': 'Latitude.', 'type': 'number',
                             'required': True, 'example': 52.5200},
            'long': {'in': 'query', 'description': 'Longitude.', 'type': 'number',
                    'required': True, 'example': 13.4050},
            'radius': {'in': 'query', 'description': 'Radius in meters.', 'type': 'number',
                     'required': True, 'example': 10000},
            'page': {'in': 'query', 'description': 'The page number of results.',
                     'type': 'int',
                     'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page.', 'type': 'int',
                         'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                lat = request.args.get('lat', 52.5200)
                long = request.args.get('long', 13.4050)
                radius = request.args.get('radius', 10000)
                page = int(request.args.get('page', 1))
                per_page = int(request.args.get('per_page', 100))

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_locations_within_range(lat, long, radius)

                cols_to_convert = df.columns.difference(['lat', 'long', 'country_id'])
                df[cols_to_convert] = df[cols_to_convert].astype(str)

                paginated_df, total = paginate_dataframe(df, page, per_page)

                result = {
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                }


                json_str = json.dumps(result, ensure_ascii=False)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_locations_for_country_sector')
    class get_locations_for_country_sector(Resource):
        @ns.doc('get_locations_for_country_sector')
        @auth.login_required
        @api.doc(params={**{
            'country_id': {'in': 'query', 'description': 'Country ID', 'type': 'number',
                             'required': True, 'example': 65, 'default': 65},
            'sector_id': {'in': 'query', 'description': 'Sector ID.', 'type': 'number',
                    'required': True, 'example': 4, 'default': 4},
            'page': {'in': 'query', 'description': 'The page number of results.',
                     'type': 'int',
                     'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page.', 'type': 'int',
                         'required': False, 'example': 100},
        }, **orient_dict})
        def get(self):
            try:
                country_id = int(request.args.get('country_id', 65))
                sector_id = int(request.args.get('sector_id', 4))

                page = int(request.args.get('page', 1))
                per_page = int(request.args.get('per_page', 100))

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_locations_for_country_sector(country_id, sector_id)

                cols_to_convert = df.columns.difference(['lat', 'long', 'country_id'])
                df[cols_to_convert] = df[cols_to_convert].astype(str)

                paginated_df, total = paginate_dataframe(df, page, per_page)

                result = {
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'data': json.loads(paginated_df.to_json(orient=orient, date_format='iso'))
                }


                json_str = json.dumps(result, ensure_ascii=False)
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_circle_geojson')
    class get_circle_geojson(Resource):
        @ns.doc('get_circle_geojson')
        @auth.login_required
        @api.doc(params={**{
            'lat': {'in': 'query', 'description': 'Latitude.', 'type': 'number',
                             'required': True, 'example': 52.5200},
            'long': {'in': 'query', 'description': 'Longitude.', 'type': 'number',
                    'required': True, 'example': 13.4050},
            'radius': {'in': 'query', 'description': 'Radius in km.', 'type': 'number',
                     'required': True, 'example': 100.5},
            'num_points': {'in': 'query', 'description': 'Number of points', 'type': 'number',
                       'required': False, 'example': 100},
        }, })
        def get(self):
            try:
                lat = float(request.args.get('lat', 52.5200))
                long = float(request.args.get('long', 13.4050))
                radius = float(request.args.get('radius', 100.5))
                num_points = int(request.args.get('num_points', 100))

                current_app.logger.info(f"{request.url}")
                json_str = gis_utils.create_geojson_circle(lat, long, radius, num_points)

                return Response(json_str, mimetype='application/geo+json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_isochrone')
    class get_isochrone(Resource):
        @ns.doc('get_isochrone')
        @auth.login_required
        @api.doc(params={**{
            'location_id': {'in': 'query', 'description': 'Location', 'type': 'string',
                             'required': True, 'example': 'edb8534f-4e29-4edd-b569-942c9965f2e2'},
            'travel_time': {
                'in': 'query',
                'description': 'Specifies the travel time for isochrone in minutes.',
                'type': 'integer',
                'required': True,
                'default': '60',
                'enum': [15, 30, 60]}
        }, })
        def get(self):
            try:
                location_id = request.args.get('location_id', 'edb8534f-4e29-4edd-b569-942c9965f2e2')
                travel_time = int(request.args.get('travel_time',60))

                current_app.logger.info(f"{request.url}")

                if travel_time in [15, 30, 60]:
                    json_str = geojson.dumps(db.get_isochrones(location_id, travel_time))
                else:
                    json_str = json.dumps({'info': 'Only 15, 30, 60 minutes isochrone is supported.'})

                return Response(json_str, mimetype='application/geo+json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_country_geometry')
    class get_country_geometry(Resource):
        @ns.doc('get_country_geometry')
        @auth.login_required
        @api.doc(params={**{
            'country_id': {'in': 'query', 'description': 'Location', 'type': 'number',
                             'required': True, 'example': '185', 'default': 185},
        }, })
        def get(self):
            try:
                country_id = request.args.get('country_id')

                current_app.logger.info(f"{request.url}")

                json_str = geojson.dumps(db.get_country_geometry(country_id))

                return Response(json_str, mimetype='application/geo+json')
            except Exception as e:
                return log_and_return_error(e)



    # @ns.route('/get_region_geometry')
    # class get_region_geometry(Resource):
    #     @ns.doc('get_region_geometry')
    #     @auth.login_required
    #     @api.doc(params={**{
    #         'region_id': {'in': 'query', 'description': 'NUTS or BEA region id', 'type': 'string',
    #                          'required': True, 'example': 'BE335', 'default': "BE335"},
    #     }, })
    #     def get(self):
    #         try:
    #             region_name = request.args.get('region_id')
    #
    #             current_app.logger.info(f"{request.url}")
    #
    #             json_str = geojson.dumps(db.get_region_geometry(region_name))
    #
    #             return Response(json_str, mimetype='application/geo+json')
    #         except Exception as e:
    #             return log_and_return_error(e)


    @ns.route('/get_region')
    class get_region(Resource):
        @ns.doc('get_region')
        @auth.login_required
        @api.doc(params={**{
            'region_id': {'in': 'query', 'description': 'NUTS or BEA region id', 'type': 'string',
                             'required': False, 'example': 'BE335', 'default': "BE335"},
            'country_id': {'in': 'query', 'description': 'Country ID, if given the API returns all regions in that country', 'type': 'number',
                          'required': False, 'example': 63, 'default': 63},
            'with_geometry': {'in': 'query',
                                   'description': 'Whether to return the GeoJson geometry of the region.',
                                   'type': 'boolean',
                                   'required': True, 'example': False, 'default': False},
        },  **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient')
                region_id = request.args.get('region_id')
                country_id = request.args.get('country_id')

                with_geometry = request.args.get('with_geometry', "False")
                with_geometry = True if with_geometry == "true" else False

                current_app.logger.info(f"{request.url}")

                df = db.get_region(region_id, country_id, with_geometry)

                if with_geometry:
                    json_str = json.dumps(df)  # Convert GeoJSON dict to JSON string
                    return Response(json_str, mimetype='application/geo+json')
                else:
                    json_str = df.to_json(orient="records", date_format="iso")  # DataFrame case
                    return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)

    @ns.route('/get_region_enriched')
    class get_region_enriched(Resource):
        @ns.doc('get_region_enriched')
        @auth.login_required
        @api.doc(params={**{
            'region_id': {'in': 'query', 'description': 'NUTS or BEA region id', 'type': 'string',
                             'required': False, 'example': 'BE335', 'default': "BE335"},
            'country_id': {'in': 'query', 'description': 'Country ID, if given the API returns all regions in that country', 'type': 'number',
                          'required': False, 'example': 63, 'default': 63},
            'with_geometry': {'in': 'query',
                                   'description': 'Whether to return the GeoJson geometry of the region.',
                                   'type': 'boolean',
                                   'required': True, 'example': False, 'default': False},
        },  **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient')
                region_id = request.args.get('region_id')
                country_id = request.args.get('country_id')

                with_geometry = request.args.get('with_geometry', "False")
                with_geometry = True if with_geometry == "true" else False

                current_app.logger.info(f"{request.url}")

                df = db.get_region_enriched(region_id, country_id, with_geometry)

                if with_geometry:
                    json_str = json.dumps(df)  # Convert GeoJSON dict to JSON string
                    return Response(json_str, mimetype='application/geo+json')
                else:
                    json_str = df.to_json(orient=orient, date_format="iso")  # DataFrame case
                    return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)


    @ns.route('/get_country_regions_geojson')
    class get_country_regions_geojson(Resource):
        @ns.doc('get_country_regions_geojson')
        @auth.login_required
        @api.doc(params={**{
            'country_id': {'in': 'query', 'description': 'Country ID, API returns a valid GeoJSON for that country', 'type': 'number',
                          'required': False, 'example': 63, 'default': 63}
        }})
        def get(self):
            try:
                country_id = request.args.get('country_id')

                current_app.logger.info(f"{request.url}")

                df = db.get_geojson_country(country_id)
                json_str = json.dumps(df.iloc[0]['geojson'])
                return Response(json_str, mimetype='application/geo+json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_region_heatmap_labels')
    class get_region_heatmap_labels(Resource):
        @ns.doc('get_region_heatmap_labels')
        @auth.login_required
        @api.doc(params={**{
            'country_id': {'in': 'query', 'description': 'Country ID, API returns a valid GeoJSON for that country', 'type': 'number',
                          'required': True, 'example': 63, 'default': 63},
            'data_type': {'in': 'query', 'description': 'Type of data.', 'type': 'string',
                         'required': True, 'enum': [e.name for e in REGION_HEATMAP_DATA],
                         'example': REGION_HEATMAP_DATA.CATEGORY.name, 'default': REGION_HEATMAP_DATA.CATEGORY.name},
        },  **orient_dict})
        def get(self):
            try:
                country_id = int(request.args.get('country_id'))
                data_type = request.args.get('data_type')
                orient = request.args.get('orient')

                current_app.logger.info(f"{request.url}")

                data_type_enum = REGION_HEATMAP_DATA[data_type]

                #print(data_type_enum.value)

                df = CSV_loader.get_region_heatmap_labels(country_id, data_type_enum.value)

                #print(df)

                json_str = df.to_json(orient=orient, date_format="iso")  # DataFrame case
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)

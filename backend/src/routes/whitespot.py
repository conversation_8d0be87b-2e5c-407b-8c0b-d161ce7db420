import pandas as pd
from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
import src.analysis.whitespot as ws
from src.routes.routes_common import orient_dict
from enum import Enum
import json
import src.utils.gis_utils as gis_utils

class WS_METRIC(Enum):
    CUSTOMER = 'customer'
    GDP = 'gdp'

def register_routes_whitespot(api, auth):
    ns = api.namespace('whitespot', description='Whitespot functions.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')
    
    @ns.route('/get_whitespots')
    class get_whitespots(Resource):
        @ns.doc('get_whitespots')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'cib_id', 'type': 'string',
                            'required': True, 'example': 'vestas.com','default':'vestas.com'},
            'country_id': {'in': 'query', 'description': 'Country ID', 'type': 'number',
                    'required': True, 'example': 63, 'default':63},
            'sector_id': {'in': 'query', 'description': 'Sector ID', 'type': 'number',
                    'required': True, 'example': 32, 'default':32},
            'top_n': {'in': 'query', 'description': 'Number of whitespots',
                    'type': 'number', 'required': True, 'example': 10, 'default':10},
            'radius': {'in': 'query', 'description': 'Radius of circles in km', 'type': 'number',
                    'required': True, 'example': 10, 'default':10},
            'metric': {'in': 'query', 'description': 'Sorting metric', 'type': 'string',
                    'required': False, 'enum':[e.name for e in WS_METRIC],'default':WS_METRIC.CUSTOMER.name}
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id', 'vestas.com')
                country_id = int(request.args.get('country_id', 63))
                sector_id = int(request.args.get('sector_id', 32))
                top_n = int(request.args.get('top_n', 10))
                radius = int(request.args.get('radius', 10))
                metric = request.args.get('metric',WS_METRIC.CUSTOMER.name)
                metric_enum = WS_METRIC[metric]
                orient = request.args.get('orient')
                current_app.logger.info(f"{request.url}")
                if metric_enum.value == "customer":
                    df = ws.get_whitespots(cib_id=cib_id,country_id=country_id,sector_id=sector_id,top_n=top_n, radius=radius)
                else:
                    df = ws.get_whitespots_gdp(cib_id=cib_id,country_id=country_id,sector_id=sector_id,top_n=top_n, radius=radius)
                df['type'] = 'whitespot'
                df_buyer = ws.get_locations(cib_id=cib_id,country_id=country_id)
                df_buyer['type'] = 'buyer'
                df_total = pd.concat([df,df_buyer])

                json_str = df_total.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
            
    @ns.route('/get_whitespot_targets')
    class get_whitespot_targets(Resource):
        @ns.doc('get_whitespot_targets')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'cib_id', 'type': 'string',
                            'required': True, 'example': 'vestas.com','default':'vestas.com'},
            'country_id': {'in': 'query', 'description': 'Country ID', 'type': 'number',
                    'required': True, 'example': 63, 'default':63},
            'sector_id': {'in': 'query', 'description': 'Sector ID', 'type': 'number',
                    'required': True, 'example': 32, 'default':32},
            'top_n': {'in': 'query', 'description': 'Number of whitespots',
                    'type': 'number', 'required': False, 'example': 10, 'default':10},
            'radius': {'in': 'query', 'description': 'Radius of circles in km', 'type': 'number',
                    'required': True, 'example': 10, 'default':10},
            'metric': {'in': 'query', 'description': 'Sorting metric', 'type': 'string',
                    'required': False, 'enum':[e.name for e in WS_METRIC],'default':WS_METRIC.CUSTOMER.name}
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id', 'vestas.com')
                country_id = int(request.args.get('country_id', 63))
                sector_id = int(request.args.get('sector_id', 32))
                top_n = int(request.args.get('top_n', 10))
                radius = int(request.args.get('radius', 10))
                orient = request.args.get('orient')
                metric = request.args.get('metric',WS_METRIC.CUSTOMER.name)
                metric_enum = WS_METRIC[metric]
                current_app.logger.info(f"{request.url}")
                if metric_enum.value == 'customer':
                    df = ws.get_whitespot_targets(cib_id=cib_id,country_id=country_id,sector_id=sector_id, radius=radius,top_n=top_n)
                else:
                    df = ws.get_whitespot_targets_gdp(cib_id=cib_id,country_id=country_id,sector_id=sector_id, radius=radius,top_n=top_n)
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
    
    @ns.route('/single_whitespot_targets')
    class single_whitespot_targets(Resource):
        @ns.doc('single_whitespot_targets')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'cib_id', 'type': 'string',
                            'required': True, 'example': 'vestas.com','default':'vestas.com'},
            'country_id': {'in': 'query', 'description': 'Country ID', 'type': 'number',
                    'required': True, 'example': 63, 'default':63},
            'sector_id': {'in': 'query', 'description': 'Sector ID', 'type': 'number',
                    'required': True, 'example': 32, 'default':32},
            'city_name': {'in': 'query', 'description': 'City / region name', 'type': 'string',
                    'required': True, 'example': 'Berlin', 'default':'Berlin'},
            'radius': {'in': 'query', 'description': 'Radius of circles in km', 'type': 'number',
                    'required': True, 'example': 10, 'default':10},
            'metric': {'in': 'query', 'description': 'Sorting metric', 'type': 'string',
                    'required': False, 'enum':[e.name for e in WS_METRIC],'default':WS_METRIC.CUSTOMER.name}
        },  **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id', 'vestas.com')
                country_id = int(request.args.get('country_id', 63))
                sector_id = int(request.args.get('sector_id', 32))
                city_name = request.args.get('city_name', 'Berlin')
                radius = int(request.args.get('radius',10))
                metric = request.args.get('metric',WS_METRIC.CUSTOMER.name)
                metric_enum = WS_METRIC[metric]
                orient = request.args.get('orient')
                current_app.logger.info(f"{request.url}")
                if metric_enum.value == 'customer':
                    df = ws.single_whitespot_targets(cib_id=cib_id,country_id=country_id,sector_id=sector_id, city=city_name, radius=radius)
                else:
                    df = ws.single_whitespot_targets_gdp(cib_id=cib_id,country_id=country_id,sector_id=sector_id, region=city_name, radius=radius)
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
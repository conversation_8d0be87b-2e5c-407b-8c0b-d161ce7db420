from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
from flask_restx import fields
from enum import Enum
import json
import pandas as pd
import src.utils.paths.csv_paths as csv_paths
from src.routes.routes_common import orient_dict, paginate_dataframe

import src.data_platform.companies_db_connector as db

class Language(Enum):
    ENGLISH = "English"
    GERMAN = "German"

class Mode(Enum):
    STEM = "Stem-based"
    EXACT = "Exact match"

def register_routes_companies_database(api, auth):
    ns = api.namespace('companies_db', description='Companies database queries.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_ranking')
    class get_ranking(Resource):
        @ns.doc('get_ranking')
        @auth.login_required
        @api.doc(params={**{
            'language': {'in': 'query', 'description': 'Language of the keyword. Can be English or German', 'type': 'string',
                        'required': True,  'enum': [e.name for e in Language], 'example': Language.GERMAN.name, 'default': Language.ENGLISH.name},
            'keyword': {'in': 'query', 'description': 'The keyword for the search.', 'type': 'string',
                                'required': True, 'example': 'revenue', 'default': 'revenue'},
            'mode': {'in': 'query', 'description': 'Search mode: stem-based, allowing plurals and inflections, or exact match.', 'type': 'string',
                        'required': True,  'enum': [e.name for e in Mode], 'example': Mode.STEM.name, 'default': Mode.EXACT.name},
            'limit': {'in': 'query', 'description': 'How many results to display.', 'type': 'integer',
                        'required': True, 'example': 100, 'default': 100}
        }, **orient_dict})
        def get(self):
            try:
                lang_string = request.args.get('language')
                keyword = request.args.get('keyword')
                mode_string = request.args.get('mode')
                limit = request.args.get('limit')
                orient = request.args.get('orient', 'split')
                current_app.logger.info(f"{request.url}")
                lang = Language[lang_string]
                mode = Mode[mode_string]

                result = db.get_occurrences_ranking(keyword=keyword, mode=mode, lang=lang, limit=limit)
                if result.empty:
                    return Response("{}", mimetype='application/json')

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
            
    @ns.route('/get_text_snippets')
    class get_text_snippets(Resource):
        @ns.doc('get_text_snippets')
        @auth.login_required
        @api.doc(params={**{
            'base_url': {'in': 'query', 'description': 'base_url for which the search is done.', 'type': 'string',
                        'required': True, 'example': 'pwc.com', 'default': 'pwc.com'},
            'language': {'in': 'query', 'description': 'Language of the keyword. Can be English or German', 'type': 'string',
                        'required': True,  'enum': [e.name for e in Language], 'example': Language.GERMAN.name, 'default': Language.ENGLISH.name},
            'keyword': {'in': 'query', 'description': 'The keyword for the search.', 'type': 'string',
                        'required': True, 'example': 'revenue', 'default': 'revenue'}
        }, **orient_dict})
        def get(self):
            try:
                base_url = request.args.get('base_url')
                lang_string = request.args.get('language')
                keyword = request.args.get('keyword')
                orient = request.args.get('orient', 'split')
                current_app.logger.info(f"{request.url}")
                lang = Language[lang_string]

                result = db.get_occurences_text(base_url = base_url, keyword=keyword, lang=lang)
                if result.empty:
                    return Response("{}", mimetype='application/json')

                json_str = result.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)
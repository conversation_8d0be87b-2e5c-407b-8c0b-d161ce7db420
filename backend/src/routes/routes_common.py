from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
import src.data_platform.CSV_connector as CSV_loader


import json

orient_dict = {
            'orient': {
                'in': 'query',
                'description': 'Specifies the format of the JSON output.',
                'type': 'string',
                'required': False,
                'default': 'split',
                'enum': ['split', 'records', 'index', 'columns', 'values', 'table']
            }
        }


def paginate_dataframe(df, page, per_page):
    total = len(df)
    start = (page - 1) * per_page
    end = start + per_page
    paginated_df = df.iloc[start:end]
    return paginated_df, total






def register_routes_common(api, auth):
    ns = api.namespace('common', description='Data pertaining to the general API usage.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')

    @ns.route('/get_all_countries')
    class get_all_countries(Resource):
        @ns.doc('get_all_countries')
        @auth.login_required
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_all_table_unsafe('countries')

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_all_regions')
    class get_all_regions(Resource):
        @ns.doc('get_all_regions')
        @auth.login_required
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_all_regions()

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_all_continents')
    class get_all_continents(Resource):
        @ns.doc('get_all_continents')
        @auth.login_required
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_all_table('continents')

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_all_sectors')
    class get_all_sectors(Resource):
        @ns.doc('get_all_sectors')
        @auth.login_required
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_all_table('sectors')

                # hack to remove some subsectors
                # List of partial matches
                partial_matches = ["information services", "car rental"]
                # Create a regex pattern from the list of partial matches
                pattern = "|".join(partial_matches)
                # Filter out rows where 'name' matches any of the partial strings (case insensitive)
                filtered_df = df[~df["sector_name"].str.contains(pattern, case=False, na=False)]

                json_str = filtered_df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)



    @ns.route('/get_all_functions')
    class get_all_functions(Resource):
        @ns.doc('get_all_functions')
        @auth.login_required
        @api.doc(params={**{
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_all_table('functions')

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_sub_sectors')
    class get_sub_sectors(Resource):
        @ns.doc('get_sub_sectors')
        @auth.login_required
        @api.doc(params={**{
            'sector_id': {'in': 'query', 'description': 'The ID of the sector.', 'type': 'integer',
                             'required': False, 'example': 7, 'default': 7},
            'level': {'in': 'query', 'description': 'Which level of sub-sectors to return.', 'type': 'integer',
                       'required': True, 'example': 1, 'default': 1},
            'max_depth': {'in': 'query', 'description': 'Max depth', 'type': 'integer',
                      'required': False, 'example': 1, 'default': 0},
        }, **orient_dict})
        def get(self):
            try:
                sector_id = request.args.get('sector_id', 7)
                level = int(request.args.get('level', 1))
                max_depth = int(request.args.get('max_depth', 0))

                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                if max_depth == 0:
                    max_depth = level + 1

                df = db.get_sub_sectors(sector_id, level, max_depth)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    api.add_namespace(ns)


    api.add_namespace(ns)


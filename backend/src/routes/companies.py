from flask import request, Response, current_app
from flask_restx import Resource
import src.data_platform.DB_connector as db
from flask_restx import fields
import json
import pandas as pd
import src.utils.paths.csv_paths as csv_paths
from src.routes.routes_common import orient_dict, paginate_dataframe


special_cib_ids_subsectors_df = pd.read_csv(csv_paths.SPECIAL_COMPANIES_WITH_SUBSECTORS, sep=",")


def register_routes_companies(api, auth):
    ns = api.namespace('companies', description='Single company data.')

    def log_and_return_error(e):
        current_app.logger.error("Error occurred: %s", str(e), exc_info=True)
        return Response(json.dumps({"error": "Internal Server Error", "message": str(e)}), status=500,
                        mimetype='application/json')



    @ns.route('/search_all_companies')
    class search_all_companies(Resource):
        @ns.doc('search_all_companies')
        @auth.login_required
        @api.doc(params={**{
            'start_string': {'in': 'query', 'description': 'The starting string of the company name. Case-insensitive.', 'type': 'string',
                     'required': True, 'example': 'Ac'},
            'page': {'in': 'query', 'description': 'The page number of results. Return full result if page = 0.', 'type': 'int',
                             'required': False, 'example': 1},
            'per_page': {'in': 'query', 'description': 'Results per page. Ignored if page = 0.', 'type': 'int',
                           'required': False, 'example': 10},
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')
                start_string = request.args.get('start_string', 'Ac')
                page = int(request.args.get('page', 0))
                per_page = int(request.args.get('per_page', 100))

                current_app.logger.info(f"{request.url}")

                allow_tax_advisors = bool(request.headers.get("allowTaxAdvisors", ""))
                df = db.search_all_companies(start_string, page, per_page, allow_tax_advisors)

                # # ugly hack, refer to https://cloud-ib.atlassian.net/browse/BAC-33
                # df2 = df.set_index('cib_id')
                # special2 = special_cib_ids_subsectors_df.set_index('cib_id')
                # df2['sector_id'].update(special2['sector_id'])
                # df = df2.reset_index()

                if page == 0:
                    json_str = df.to_json(orient=orient, date_format='iso')
                else:
                    result = {
                        'page': page,
                        'per_page': per_page,
                        'data': json.loads(df.to_json(orient=orient, date_format='iso'))
                    }
                    json_str = json.dumps(result, ensure_ascii=False)


                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_by_cib_id')
    class get_by_cib_id(Resource):
        @ns.doc('get_by_cib_id')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code for the company.', 'type': 'string',
                             'required': False, 'example': 'vestas.com', 'default': 'vestas.com'},
            'get_random_company': {'in': 'query', 'description': 'Whether to return a random company. If true, the parameter above is ignored.', 'type': 'boolean',
                       'required': True, 'example': False, 'default': False},
        }, **orient_dict})
        def get(self):
            try:
                orient = request.args.get('orient', 'split')
                cib_id = request.args.get('cib_id', 'vestas.com')
                get_random_company = request.args.get('get_random_company', "False")
                get_random_company = True if get_random_company == "true" else False

                current_app.logger.info(f"{request.url}")

                df = db.get_by_cib_id(cib_id if not get_random_company else None)

                json_str = df.to_json(orient=orient, date_format='iso')

                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    cib_ids_model = {
        'cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of companies.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'orient': fields.String(
            description = 'DF format',
            required = True,
            default= 'records')}


    @ns.route('/get_by_cib_id_bulk')
    class get_by_cib_id_bulk(Resource):
        @ns.doc('get_by_cib_id_bulk')
        @auth.login_required
        @api.expect(api.model('CibIdsModel', cib_ids_model))
        def post(self):
            try:
                data = request.json

                target_cib_ids = data.get('cib_ids', [])
                orient = data.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                df = db.get_multiple_cib_ids(target_cib_ids)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_company_locations')
    class get_company_locations(Resource):
        @ns.doc('get_company_locations')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
            'country_id': {'in': 'query', 'description': 'The ID code of the location country. If empty, returns locations accross all countries.', 'type': 'integer',
                       'required': False, 'example': 63, 'default': ''},
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id')
                country_id = request.args.get('country_id', '')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                df = db.get_company_locations(cib_id, country_id if country_id else None)

                cols_to_convert = df.columns.difference(['functions', 'lat', 'long', 'country_id'])  # Exclude 'functions' column, needs a list
                df[cols_to_convert] = df[cols_to_convert].astype(str)

                json_str = df.to_json(orient=orient, date_format='iso')

                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    companies_locations_payload = {
        'cib_ids': fields.List(
            fields.String,
            required=True,
            description='List of company IDs.',
            example=['vestas.com', 'nordex-online.com']
        ),
        'country_id': fields.Integer(
            required=False,
            description='Filter locations by country ID.'
        ),
        'orient': fields.String(
            description='DF format',
            required=True,
            default='records')}


    @ns.route('/get_multiple_companies_locations')
    class get_multiple_companies_locations(Resource):
        @ns.doc('get_multiple_companies_locations')
        @auth.login_required
        @api.expect(api.model('CompaniesLocationsPayload', companies_locations_payload))
        def post(self):
            try:
                data = request.json
                cib_ids = data.get('cib_ids', [])
                country_id = data.get('country_id')
                orient = data.get('orient', 'split')

                if not cib_ids:
                    return {"error": "cib_ids is required"}, 400

                current_app.logger.info(f"{request.url} - cib_ids={cib_ids}, country_id={country_id}")

                df = db.get_multiple_companies_locations(cib_ids, country_id)

                # Convert everything except `functions`, `lat`, `long`, `country_id` into strings
                cols_to_convert = df.columns.difference(['functions', 'lat', 'long', 'country_id'])
                df[cols_to_convert] = df[cols_to_convert].astype(str)

                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')

            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_functional_geo_availables')
    class get_functional_geo_availables(Resource):
        @ns.doc('get_functional_geo_availables')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
        }, **orient_dict})
        def get(self):
            try:
                lei = request.args.get('cib_id')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")

                df = db.get_functional_geo_availables(lei)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    @ns.route('/get_company_countries_by_locations')
    class get_company_countries_by_locations(Resource):
        @ns.doc('get_company_countries_by_locations')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_company_countries_by_locations(cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)



    @ns.route('/get_company_functions')
    class get_company_functions(Resource):
        @ns.doc('get_company_functions')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company.', 'type': 'string',
                             'required': True, 'example': 'academy.com'},
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_company_functions(cib_id)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)

    @ns.route('/get_company_data_source')
    class get_company_data_source(Resource):
        @ns.doc('get_company_data_source')
        @auth.login_required
        @api.doc(params={**{
            'cib_id': {'in': 'query', 'description': 'The ID code of the company.', 'type': 'string',
                             'required': True, 'example': 'vestas.com'},
        }, **orient_dict})
        def get(self):
            try:
                cib_id = request.args.get('cib_id')
                orient = request.args.get('orient', 'split')

                current_app.logger.info(f"{request.url}")
                df = db.get_location_source(cib_id)
                df[df.columns] = df[df.columns].astype(str)

                # Convert the DataFrame to a JSON string, ensuring NaN/None values are converted to null
                json_str = df.to_json(orient=orient, date_format='iso')
                return Response(json_str, mimetype='application/json')
            except Exception as e:
                return log_and_return_error(e)


    api.add_namespace(ns)


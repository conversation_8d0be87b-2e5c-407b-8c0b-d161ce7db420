
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings

engine = db_settings.ENGINE_MANAGER.get_or_create()


def get_country_intersection(buyer_cib_id, target_cib_ids, engine_or_conn=None):
    if engine_or_conn is None:
        engine_or_conn = engine
    
    # Parameterized query with placeholders
    query = text("""
      SELECT
        l2.cib_id AS target_cib_id,
        (SELECT array_agg(DISTINCT cr.old_country_id)
         FROM locations l
         JOIN countries cr on l.country_id = cr.country_id
         WHERE cib_id = :buyer_cib_id
        ) AS buyer_countries,
        (SELECT array_agg(DISTINCT cr.old_country_id)
         FROM locations l
         JOIN countries cr on l.country_id = cr.country_id
         WHERE cib_id = l2.cib_id
        ) AS target_countries,
        (SELECT array_agg(DISTINCT cr.old_country_id)
         FROM locations l1
         JOIN locations inner_l2 USING (country_id)  -- Correct alias placement
         JOIN countries cr on l1.country_id = cr.country_id
         WHERE l1.cib_id = :buyer_cib_id
           AND inner_l2.cib_id = l2.cib_id  -- Ensure correct comparison
        ) AS common_countries
      FROM locations l2
      WHERE l2.cib_id IN :target_cib_ids
      GROUP BY l2.cib_id;
    """)

    params = {
        "buyer_cib_id": buyer_cib_id,
        "target_cib_ids": tuple(target_cib_ids)   # Convert list to PostgreSQL array format
    }

    # Execute the query with parameters
    result = pd.read_sql(query, engine_or_conn, params=params)


    def calculate_indic_value(row):
        common = row['common_countries'] if row['common_countries'] is not None else []
        target = row['target_countries'] if row['target_countries'] is not None else []

        indic_value = len(common) / len(target) if len(target) > 0 else 0
        common_num = len(common)
        target_num = len(target)
        label = f"{common_num} out of {target_num} countries overlap"

        return indic_value, label


    result['indic_value'] = result.apply(lambda x: calculate_indic_value(x)[0], axis=1)

    result['common_countries'] = result['common_countries'].apply(lambda x: [] if x is None else x)
    result['common_countries_percent'] = result.apply(lambda x: calculate_indic_value(x)[0], axis=1)
    result['column_name'] = 'common_countries_percent'

    result['column_label'] = result.apply(lambda x: calculate_indic_value(x)[1], axis=1)


    return result


#x = get_country_intersection('nordex-online.com', ['vestas.com', 'juliusbaer.com'])
#print(x)
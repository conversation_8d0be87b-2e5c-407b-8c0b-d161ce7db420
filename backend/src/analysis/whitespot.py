'''
Functions that query the database to generate whitespots for companies, as well as potential targets that would 
fill the whitespots (all or one).

get_whitespots() looks through all the cities in a country and analyses how big the population is (population) how many customers are in a radius around each 
city (total_customers), as well as the number of customers that a whitespot circle would bring net of customers in circles 
around buyer's existing locations (net_customers). Top_n cities (sorted by net_customers DESC) are returned.

get_whitespot_targets() finds all companies that are mostly present in the specified country and are not too much bigger than the buyer,
and that fill the whitespots the best. For each target, total number of customers in its radiuses (total_customers), customers that are also
in whitespots (ws_customers) and net/delta customers net of buyer existing customers (net_customers).

single_whitespot_targets() finds companies which have at least 50% of their locations near the whitespot, and ranks them similar to other
functions, so that they provide the most customers that are in the whitespot and not existing buyer customers.
'''

import pandas as pd
from sqlalchemy import Engine, text
from src.config import rep_db_settings
#get_clickhouse_client switch to Postgres

engine: Engine = rep_db_settings.ENGINE_MANAGER.get_or_create()

CITY_THRES = 0.25
WHITESPOT_RADIUS = 25000

def get_whitespots(cib_id:str='vestas.com', country_id:int=63,sector_id:int=32,top_n:int=25, radius:int=10)->pd.DataFrame:
    '''Returns whitespots that represent cities which would bring the most additional customers to a company.
    Calculated on h3 level, with partial hexagons taken as whole.'''
    radius = int(radius) * 1000
    total_radius = radius + WHITESPOT_RADIUS
    query = f'''
        WITH buyer_locs AS (
            SELECT geometry_3857, location_id
            FROM locations loc
            WHERE loc.cib_id = :cib_id AND loc.country_id = :country_id
        ),
        city_locs AS (
            SELECT cp.geometry_3857,city,population,
            cp.lng, cp.lat, cp.id
            FROM cities_population cp
            WHERE cp.country_id = :country_id AND
                    NOT EXISTS(
                        SELECT location_id
                        FROM buyer_locs bl
                        WHERE ST_DWithin(bl.geometry_3857,cp.geometry_3857,:total_radius)
                    )
            ORDER BY population DESC
            LIMIT 100
        )
        SELECT
            cl.id as city_id,cl.city,cl.lng as long,cl.lat,cl.population as city_population,
            kp.h3, kp.population,
             EXISTS(
                SELECT geometry_3857
                FROM buyer_locs bl
                WHERE ST_DWithin(kp.geometry_3857,bl.geometry_3857,:radius)
             ) as buyer_radius
        FROM 
            kontur_population kp
        JOIN 
            city_locs cl ON ST_DWithin(kp.geometry_3857,cl.geometry_3857,:ws_radius)
    '''
    params = {
        'sector_id':sector_id,
        'country_id':country_id,
        'top_n':top_n,
        'cib_id':cib_id,
        'radius':radius,
        'ws_radius':WHITESPOT_RADIUS,
        'total_radius':total_radius,
        'city_t':CITY_THRES,
        }
    df = pd.read_sql(text(query),engine,params=params)
    df_orig = df[['city_id','city','lat','long','city_population']].drop_duplicates()
    city_list = []
    all_data = []
    
    # Filter based on population
    totals_in = df.groupby(['city_id','city_population'])['population'].sum().rename('total_customers').reset_index()
    mask = totals_in['city_population'] >= totals_in['total_customers'] * CITY_THRES
    valid_cities = totals_in.loc[mask,'city_id'].to_list()
    print(valid_cities)
    df = df[df['city_id'].isin(valid_cities)]

    while len(city_list) < top_n:
        totals = df.groupby('city_id')['population'].sum().rename('total_customers').reset_index()
        nets = df[df['buyer_radius'] == False].groupby('city_id')['population'].sum().rename('net_customers').reset_index()
        candidates = pd.merge(totals,nets,on='city_id').sort_values('net_customers',ascending=False).reset_index()
        if candidates.empty:
            break
        new_city = candidates.iloc[0]
        city_id = new_city['city_id']
        city_list.append(city_id)
        all_data.append(new_city[['city_id','total_customers','net_customers']].to_list())
        h3s = df.loc[df['city_id'] == city_id,'h3'].to_list()
        df = df[(~df['city_id'].isin(city_list)) & (~df['h3'].isin(h3s))] # Remove the city and the related h3 hexagons

    resulting_df = pd.DataFrame(all_data,columns=['city_id','total_customers','net_customers'])
    final_df = pd.merge(left=resulting_df, right=df_orig, on='city_id', how='left').rename(columns={'city_population':'population'})

    return final_df

def get_locations(cib_id:str='vestas.com',country_id:int=63)->pd.DataFrame:
    '''Gets company name and returns a dataframe with locations'''
    params = {'cib_id':cib_id,'country_id':country_id}
    query = text('''
                 SELECT long, lat FROM locations
                 WHERE country_id = :country_id AND cib_id = :cib_id
                 ''')
    res = pd.read_sql(query, engine, params=params)
    return res

def get_whitespot_targets_old(cib_id:str,country_id:int,sector_id:int,radius:int=10,top_n:int=10):
    '''For company A in country B, look for targets from sector C that will bring the most new customers
    in whitespots in cities D'''
    cities_manual = get_whitespots(cib_id=cib_id,country_id=country_id,sector_id=sector_id,radius=radius,top_n=top_n)
    radius = radius * 1000
    cities_manual = tuple(cities_manual['city'])
    print(cities_manual)
    query = text(f'''
        WITH buyer_locs AS(
            SELECT geometry_3857
            FROM locations
            WHERE cib_id = :cib_id AND country_id = :country_id
        ),
        whitespots AS(
            SELECT cp.city, geometry_3857
            FROM cities_population cp
            WHERE cp.city IN :cities
        ),
        target_locs_all as (
        	select lt.cib_id,geometry_3857
            from(
        		select l.cib_id
        		from locations l
        		LEFT JOIN company_sectors cs
        	        ON cs.cib_id = l.cib_id
        	    WHERE cs.sector_id = :sector_id
        	    GROUP BY l.cib_id
        	        HAVING COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) >= 0.75 * COUNT(l.location_id) AND
                    COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) <= 3 * (SELECT COUNT(*) FROM buyer_locs)
        	) as lt
        	left join locations l
        		on l.cib_id = lt.cib_id
        	where l.country_id = :country_id
    	),
        target_locs as (
            select *
            from target_locs_all
            where cib_id IN (
                select cib_id
                from target_locs_all tla
                where exists(
                    select ws.geometry_3857
                    from whitespots ws
                    where ST_DWithin(ws.geometry_3857, tla.geometry_3857, :ws_radius)
                )
            )
        ),
        hexes as (
        	select kp.h3, kp.geometry_3857,kp.population,tl.cib_id
        	from kontur_population kp
        	right join target_locs tl
        	on ST_DWithin(kp.geometry_3857,tl.geometry_3857,:radius)
        ),
        data as (
            SELECT distinct hexes.cib_id,hexes.population,
                EXISTS(
                    select ws.city
                    from whitespots ws
                    where ST_DWithin(ws.geometry_3857, h.geometry_3857, :ws_radius)
                ) as ws_radius,
                EXISTS(
                    select bl.geometry_3857
                    from buyer_locs bl
                    where ST_DWithin(bl.geometry_3857, h.geometry_3857, :radius)
                ) as buyer_radius
            FROM (SELECT distinct on (h3) geometry_3857 FROM hexes) h
            RIGHT JOIN hexes
                 ON hexes.geometry_3857 = h.geometry_3857
        ),
        totals as (
            select cib_id,
                SUM(population) as total_customers,
                SUM(case when ws_radius = True then population else 0 END) as ws_customers,
                SUM(case when ws_radius = True and buyer_radius = False then population else 0 END) as net_customers
            from data
            group by cib_id
        )
        select t.cib_id, c.company_name, t.total_customers, t.ws_customers, t.net_customers
        from totals t
        left join companies c
            on t.cib_id = c.cib_id
        order by net_customers desc
        limit 50
        ''')
    params = {
        'radius':radius,
        'ws_radius':WHITESPOT_RADIUS,
        'cib_id':cib_id,
        'country_id':country_id,
        'sector_id':sector_id,
        'cities':cities_manual
    }
    res = pd.read_sql(query, engine, params=params)
    return res

def get_whitespot_targets(cib_id:str,country_id:int,sector_id:int,radius:int=10,top_n:int=10):
    '''For company A in country B, look for targets from sector C that will bring the most new customers
    in whitespots in cities D'''
    cities_manual = get_whitespots(cib_id=cib_id,country_id=country_id,sector_id=sector_id,radius=radius,top_n=top_n)
    radius = radius * 1000
    cities_manual = tuple(cities_manual['city_id'])
    print(cities_manual)
    query = text(f'''
        WITH candidates AS(
            SELECT l.cib_id
            FROM locations l
            JOIN company_sectors cs
                ON cs.cib_id = l.cib_id
            WHERE cs.sector_id = :sector_id
            GROUP BY l.cib_id
                HAVING COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) >= 0.75 * COUNT(l.location_id) AND
                COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) <= 3 * (SELECT COUNT(*) FROM locations WHERE cib_id = :cib_id AND country_id = :country_id)
        ),
        hexes AS(
            SELECT DISTINCT ON (l.cib_id,kp.h3) l.cib_id,kp.h3, kp.population, kp.geometry_3857, cp.city
            FROM cities_population cp
            JOIN kontur_population kp on ST_DWithin(cp.geometry_3857, kp.geometry_3857,:ws_radius)
            join locations l on ST_DWithin(l.geometry_3857, kp.geometry_3857,:radius)
            where cp.id in :cities and l.country_id = :country_id and l.cib_id in (select cib_id from candidates)
        ),
        targets as(
            SELECT cib_id, SUM(population) as net_customers
            FROM hexes
            group by cib_id
            order by SUM(population) desc
            limit :targ
        ),
        totals as(
            SELECT cib_id, SUM(population) AS total_customers
            FROM (
                SELECT DISTINCT ON (l.cib_id, kp.h3) l.cib_id, kp.population
                FROM locations l
                JOIN kontur_population kp ON ST_DWithin(kp.geometry_3857, l.geometry_3857,:radius)
                WHERE l.cib_id IN (SELECT cib_id FROM targets)
            )
            GROUP BY cib_id
        )
        SELECT t.cib_id, c.company_name, tot.total_customers, t.net_customers
        FROM targets t
        JOIN totals tot ON t.cib_id = tot.cib_id
        JOIN companies c on t.cib_id = c.cib_id
        ORDER BY t.net_customers desc
        ''')
    params = {
        'radius':radius,
        'ws_radius':WHITESPOT_RADIUS,
        'cib_id':cib_id,
        'country_id':country_id,
        'sector_id':sector_id,
        'cities':cities_manual,
        'targ':25,
    }
    res = pd.read_sql(query, engine, params=params)
    res['ws_customers'] = res['net_customers']
    return res

def single_whitespot_targets(city:str,cib_id:str,country_id:int,sector_id:int,radius:int):
    radius = radius * 1000
    total_radius = WHITESPOT_RADIUS + radius
    params = {
        'sector_id':sector_id,
        'radius':radius,
        'ws_radius':WHITESPOT_RADIUS,
        'city':city,
        'country_id':country_id,
        'cib_id':cib_id,
        'total_radius':total_radius,
    }
    query = text("""
       with whitespot_locs AS (
            SELECT geometry_3857
            FROM cities_population cp
            WHERE country_id = :country_id AND city = :city
            LIMIT 1
        ),
        target_locations as(
            select ti.cib_id, geometry_3857
            from (
                select l.cib_id, 1.0*COUNT(case when ST_DWithin(geometry_3857,(SELECT * FROM whitespot_locs),:total_radius) then true END)/COUNT(l.location_id) as perc
                from company_sectors cs
                left join locations l
                    on cs.cib_id = l.cib_id
                where cs.sector_id = :sector_id
                group by l.cib_id
            ) as ti
            left join locations l
            on ti.cib_id = l.cib_id
            where ti.perc >= 0.5
        ),
        buyer_locs as(
            SELECT geometry_3857
            FROM locations
            WHERE cib_id = :cib_id AND country_id = :country_id
        ),
        hexes as (
            SELECT kp.geometry_3857,kp.h3,kp.population,tl.cib_id
            FROM target_locations tl
            LEFT JOIN kontur_population kp
                ON ST_DWithin(kp.geometry_3857,tl.geometry_3857,:radius)
            group by kp.geometry_3857,kp.h3,kp.population,tl.cib_id
        ),
        data AS (
            SELECT h.geometry_3857,hexes.population,hexes.cib_id,
                EXISTS(
                    SELECT *
                    FROM buyer_locs bl
                    WHERE ST_DWithin(h.geometry_3857, bl.geometry_3857, :radius)
                ) as buyer_radius,
                EXISTS(
                    SELECT *
                    FROM whitespot_locs ws
                    WHERE ST_DWithin(h.geometry_3857, ws.geometry_3857, :ws_radius)
                ) as ws_radius
            FROM (SELECT DISTINCT ON (h3) geometry_3857 FROM hexes) h
            RIGHT JOIN hexes
                 ON h.geometry_3857 = hexes.geometry_3857
        ),
        totals as (
            select cib_id,
                SUM(population) as total_customers,
                SUM(case when ws_radius = True then population else 0 END) as ws_customers,
                SUM(case when ws_radius = True and buyer_radius = False then population else 0 END) as net_customers
            from data
            group by cib_id
        )
        select t.cib_id, c.company_name, t.total_customers, t.ws_customers, t.net_customers
        from totals t
        left join companies c
            on t.cib_id = c.cib_id
        order by net_customers desc
        limit 50
    """)
    res = pd.read_sql(query, engine, params=params)
    return res

def gdp_per_h3(country_id:int)->pd.DataFrame:
    query = f'''
        SELECT h3, gdp_usd
        FROM kontur_gdp_population
        WHERE country_id = {country_id}
        '''
    #return get_clickhouse_client().query_df(query) # ClickHouse has been removed, switch to Postgres
    return pd.DataFrame()

def get_whitespots_gdp(cib_id:str='vestas.com', sector_id:int=32,country_id:int=63,radius:int=10,top_n:int=10):
    radius = radius * 1000
    h3_gdp = gdp_per_h3(country_id)
    params = {
        'cib_id':cib_id,
        'country_id':country_id,
        'radius':radius,
        'top_n':top_n
        }
    query = text(f'''
        with nuts3 as(
            SELECT n.*,kp.geometry_3857,kp.h3
            FROM(
                select centroid_3857, ngp.nuts_code, ngp.region, ngp.gdp_eur_mil_2021 as gdp_2021
                from nuts_gdp_population ngp  
                join regional_gdp_no_geometry rg
                    on ngp.nuts_code = rg.region_id
                where nuts_level = 3 AND rg.country_id = :country_id
                order by ngp.gdp_eur_mil_2021 DESC
                limit 100
                ) as n
            left join kontur_population kp 
                on ST_DWithin(centroid_3857,kp.geometry_3857,:radius)
        ),
        buyer_locs AS (
            SELECT geometry_3857, location_id
            FROM locations loc
            WHERE loc.cib_id = :cib_id AND loc.country_id = :country_id
        )
        select ST_X(ST_Transform(centroid_3857, 4326)) as long,
               ST_Y(ST_Transform(centroid_3857, 4326)) as lat,
                 region, gdp_2021, buyer_radius, h3
        from (
            select n.centroid_3857, n.nuts_code, n.region, n.gdp_2021, n.h3,
                    COUNT(bl.location_id)>0 as buyer_radius
            from nuts3 n
            left join buyer_locs bl
                on ST_DWithin(n.geometry_3857,bl.geometry_3857,:radius)
            group by n.centroid_3857, n.nuts_code, n.region, n.gdp_2021, n.h3
            )
        ''')
    res = pd.read_sql(query, engine, params=params)
    if res.empty:
        return res
    df = pd.merge(left=res, right=h3_gdp, on='h3', how='left')
    groupcols = ['long','lat','region','gdp_2021']
    total_gdp = df.groupby(groupcols)['gdp_usd'].sum().rename('total_gdp').reset_index()
    net_gdp = df[~df['buyer_radius']].groupby(groupcols)['gdp_usd'].sum().rename('net_gdp').reset_index()

    df = pd.merge(total_gdp, net_gdp, on=groupcols,how='left')
    df = df.sort_values(by='net_gdp',ascending=False)
    df['region_clean'] = df['region'].str.replace(
        r"^([^(),]+), (.+)$", r"\1 (\2)", regex=True
    ).str.replace(
        r"^(.*)\(([^()]+)\), (.+)$", r"\1(\2, \3)", regex=True
    )
    df['gdp_2021'] = df['gdp_2021'] * 10**6 #since tabular gdp is in millions, convert to absolute number
    return df.iloc[:top_n]


def get_whitespot_targets_gdp(cib_id:str,country_id:int,sector_id:int,radius:int=10,top_n:int=10):
    '''For company A in country B, look for targets from sector C that will bring the most GDP
    in whitespots'''
    nuts_regions = get_whitespots_gdp(cib_id=cib_id,country_id=country_id,sector_id=sector_id,radius=radius,top_n=top_n)
    radius = radius * 1000
    nuts_regions = tuple(nuts_regions['region'])
    print(nuts_regions)
    h3_gdp = gdp_per_h3(country_id)

    query = text(f'''
        WITH buyer_locs AS(
            SELECT geometry_3857
            FROM locations
            WHERE cib_id = :cib_id AND country_id = :country_id
        ),
        whitespots AS(
            SELECT centroid_3857, ngp.nuts_code, ngp.region
            FROM nuts_gdp_population ngp
            WHERE ngp.region IN :nuts_regions
        ),
        target_locs as (
        	select lt.cib_id, geometry_3857
            from(
        		select l.cib_id
        		from locations l
        		LEFT JOIN company_sectors cs
        	        ON cs.cib_id = l.cib_id
        	    WHERE cs.sector_id = :sector_id
        	    GROUP BY l.cib_id
        	        HAVING COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) >= 0.75 * COUNT(l.location_id) AND
                    COUNT(CASE WHEN l.country_id = :country_id THEN 1 END) <= 3 * (SELECT COUNT(*) FROM buyer_locs)
        	) as lt
        	left join locations l
        		on l.cib_id = lt.cib_id
        	where l.country_id = :country_id
    	),
        hexes as (
        	select kp.geometry_3857,kp.h3,tl.cib_id
        	from target_locs tl
        	left join kontur_population kp
        	on ST_DWithin(kp.geometry_3857,tl.geometry_3857,:radius)
        ),
        hexes_with_flags AS (
            SELECT h.h3,
                   BOOL_OR(CASE WHEN bl.geometry_3857 IS NOT NULL THEN TRUE ELSE FALSE END) AS buyer_radius,
                   BOOL_OR(CASE WHEN ws.centroid_3857 IS NOT NULL THEN TRUE ELSE FALSE END) AS whitespot_radius
            FROM hexes h
            LEFT JOIN buyer_locs bl ON ST_DWithin(h.geometry_3857, bl.geometry_3857, :radius)
            LEFT JOIN whitespots ws ON ST_DWithin(h.geometry_3857, ws.centroid_3857, :radius)
            GROUP BY h.h3
        )
        SELECT distinct h.cib_id, c.company_name, h.h3, hf.buyer_radius, hf.whitespot_radius
        FROM hexes h
        LEFT JOIN hexes_with_flags hf
                 ON h.h3 = hf.h3
        LEFT JOIN companies c
                 ON c.cib_id = h.cib_id
        ''')
    params = {
        'radius':radius,
        'cib_id':cib_id,
        'country_id':country_id,
        'sector_id':sector_id,
        'nuts_regions':nuts_regions
    }
    res = pd.read_sql(query, engine, params=params)
    if res.empty:
        return res
    df = pd.merge(left=res, right=h3_gdp, on='h3', how='left') # Merge with mapping of h3 hexagons to GDP
    total_gdp = df.groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('total_gdp').reset_index()
    ws_gdp = df[df['whitespot_radius']].groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('whitespot_gdp').reset_index()
    net_gdp = df[(df['whitespot_radius']) & (~df['buyer_radius'])].groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('net_gdp').reset_index()
    
    df = pd.merge(total_gdp, ws_gdp, on=['cib_id','company_name'],how='left')
    df = pd.merge(df, net_gdp, on=['cib_id','company_name'],how='left')
    df = df.sort_values(by='net_gdp',ascending=False)
    df_out = df[['cib_id','company_name','total_gdp','whitespot_gdp','net_gdp']]
    return df_out.iloc[:50]

def single_whitespot_targets_gdp(cib_id:str,country_id:int,sector_id:int,radius:int,region:str):
    radius = radius * 1000
    h3_gdp = gdp_per_h3(country_id)
    params = {
        'sector_id':sector_id,
        'radius':radius,
        'region':region,
        'country_id':country_id,
        'cib_id':cib_id
    }
    query = text("""
       with whitespot AS (
            SELECT centroid_3857
            FROM nuts_gdp_population ngp
            WHERE ngp.region = :region
                 LIMIT 1
        ),
        target_locations as(
            select ti.cib_id,geometry_3857
            from (
                select l.cib_id, 1.0*COUNT(case when ST_DWithin(l.geometry_3857,(SELECT * FROM whitespot),:radius*2) then true END)/COUNT(l.location_id) as perc
                from company_sectors cs
                left join locations l
                    on cs.cib_id = l.cib_id
                where cs.sector_id = :sector_id
                group by l.cib_id
            ) as ti
            left join locations l
            on ti.cib_id = l.cib_id
            where ti.perc >= 0.5
        ),
        buyer_locs as(
            SELECT geometry_3857
            FROM locations
            WHERE cib_id = :cib_id AND country_id = :country_id
        ),
        hexes as (
        	select kp.geometry_3857,kp.h3,tl.cib_id
        	from target_locations tl
        	left join kontur_population kp
        	on ST_DWithin(kp.geometry_3857,tl.geometry_3857,:radius)
        ),
        hexes_with_flags AS (
            SELECT h.h3,
                   BOOL_OR(CASE WHEN bl.geometry_3857 IS NOT NULL THEN TRUE ELSE FALSE END) AS buyer_radius,
                   BOOL_OR(CASE WHEN ws.centroid_3857 IS NOT NULL THEN TRUE ELSE FALSE END) AS whitespot_radius
            FROM hexes h
            LEFT JOIN buyer_locs bl ON ST_DWithin(h.geometry_3857, bl.geometry_3857, :radius)
            LEFT JOIN whitespot ws ON ST_DWithin(h.geometry_3857, ws.centroid_3857, :radius)
            GROUP BY h.h3
        )
        SELECT distinct h.cib_id, c.company_name, h.h3, hf.buyer_radius, hf.whitespot_radius
        FROM hexes h
        LEFT JOIN hexes_with_flags hf
                 ON h.h3 = hf.h3
        LEFT JOIN companies c
                 ON c.cib_id = h.cib_id
    """)
    res = pd.read_sql(query, engine, params=params)
    if res.empty:
        return res
    df = pd.merge(left=res, right=h3_gdp, on='h3', how='left') # Merge with mapping of h3 hexagons to GDP
    total_gdp = df.groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('total_gdp').reset_index()
    ws_gdp = df[df['whitespot_radius']].groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('whitespot_gdp').reset_index()
    net_gdp = df[(df['whitespot_radius']) & (~df['buyer_radius'])].groupby(['cib_id','company_name'])['gdp_usd'].sum().rename('net_gdp').reset_index()
    
    df = pd.merge(total_gdp, ws_gdp, on=['cib_id','company_name'],how='left')
    df = pd.merge(df, net_gdp, on=['cib_id','company_name'],how='left')
    df = df.sort_values(by='net_gdp',ascending=False)

    df_out = df[['cib_id','company_name','total_gdp','whitespot_gdp','net_gdp']]
    return df_out.iloc[:50]

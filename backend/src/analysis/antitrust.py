import src.data_platform.DB_connector as db
import src.data_platform.geo_queries as geo_queries
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point
from sqlalchemy import text
import networkx as nx

pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)


def filter_locations_geopandas(all_locations, lat, lon, X, cib_ids):
    """
    Filter locations within X km of (lat, lon) and cib_id in cib_ids using GeoPandas.
    """
    # Convert DataFrame to GeoDataFrame
    all_locations["geometry"] = all_locations.apply(
        lambda row: Point(row["long"], row["lat"]), axis=1
    )
    gdf = gpd.GeoDataFrame(all_locations, geometry="geometry", crs="EPSG:4326")

    # Create a GeoSeries for the reference point
    reference_point = gpd.GeoSeries([Point(lon, lat)], crs="EPSG:4326")

    # Reproject to a metric system (meters) for accurate distance calculation
    gdf = gdf.to_crs("EPSG:3857")
    reference_point = reference_point.to_crs("EPSG:3857")

    # Create a buffer of X kilometers (X * 1000 meters)
    buffer = reference_point.buffer(X * 1000).iloc[0]

    # Filter locations within the buffer and cib_id condition
    filtered = gdf[gdf["geometry"].within(buffer) & gdf["cib_id"].isin(cib_ids)]

    # Convert back to original CRS (optional)
    return filtered.to_crs("EPSG:4326")

def get_overlapping_locations(buyer_cib_id, target_cib_id, other_locations, country_id, radius_km=150):
    # Fetch locations for buyer, target, and others
    locations_buyer_target = db.get_locations_by_cib_id_simple([buyer_cib_id, target_cib_id], country_id)
    locations_other = pd.DataFrame() if len(other_locations) == 0 else db.get_locations_by_ids(other_locations, country_id)

    # Combine all locations into a single DataFrame
    result = pd.concat([locations_buyer_target, locations_other], axis=0)

    # Convert 'location_id' to string to avoid UUID objects
    result['location_id'] = result['location_id'].apply(str)

    result = result.drop_duplicates(subset="location_id")
    result.reset_index(drop=True, inplace=True)

    # Assign types to the locations
    result['type'] = 'other'
    result.loc[result['cib_id'] == buyer_cib_id, 'type'] = 'buyer'
    result.loc[result['cib_id'] == target_cib_id, 'type'] = 'target'

    # Select relevant columns
    result = result[['type', 'cib_id', 'location_id', 'location_name',
                     'full_address', 'country_id', 'lat', 'long']]


    # Convert DataFrame to GeoDataFrame with geometry
    result['geometry'] = [Point(xy) for xy in zip(result['long'], result['lat'])]
    gdf = gpd.GeoDataFrame(result, geometry='geometry')

    # Set Coordinate Reference System to WGS84 (lat/lon)
    gdf.set_crs(epsg=4326, inplace=True)

    # Project to a metric CRS for accurate distance calculations
    gdf = gdf.to_crs(epsg=3857)  # EPSG:3857 uses meters as units

    # Separate target locations
    target_locations = gdf[gdf['type'] == 'target'].copy()

    # Create buffers around target locations with the specified radius (converted to meters)
    target_buffers = target_locations.copy()
    target_buffers['geometry'] = target_buffers.geometry.buffer(radius_km * 1000)

    # Spatial join to find buyer locations within each target's buffer
    buyer_locations = gdf[gdf['type'] == 'buyer']  # Only buyer locations

    overlapping_buyer_locations = gpd.sjoin(
        buyer_locations,  # Left GeoDataFrame
        target_buffers[['location_id', 'geometry']],  # Right GeoDataFrame
        how='inner',
        predicate='intersects'
    )

    # Rename columns for clarity
    overlapping_buyer_locations.rename(
        columns={
            'location_id_left': 'location_id',
            'location_id_right': 'target_location_id'
        },
        inplace=True
    )

    # Convert IDs to strings
    overlapping_buyer_locations['location_id'] = overlapping_buyer_locations['location_id'].apply(str)
    overlapping_buyer_locations['target_location_id'] = overlapping_buyer_locations['target_location_id'].apply(str)



    # Spatial join to find other locations within each target's buffer
    other_locs = gdf[gdf['type'] == 'other']  # Only other locations
    overlapping_other_locations = gpd.sjoin(
        other_locs,  # Left GeoDataFrame
        target_buffers[['location_id', 'geometry']],  # Right GeoDataFrame
        how='inner',
        predicate='intersects'
    )

    # Rename columns for clarity
    overlapping_other_locations.rename(
        columns={
            'location_id_left': 'location_id',
            'location_id_right': 'target_location_id'
        },
        inplace=True
    )

    # Convert IDs to strings
    overlapping_other_locations['location_id'] = overlapping_other_locations['location_id'].apply(str)
    overlapping_other_locations['target_location_id'] = overlapping_other_locations['target_location_id'].apply(str)

    # Reset CRS to original if needed
    overlapping_buyer_locations = overlapping_buyer_locations.to_crs(epsg=4326)
    overlapping_other_locations = overlapping_other_locations.to_crs(epsg=4326)

    target_locations = target_locations.to_crs(epsg=4326)

    # Initialize the result dictionary with all target_location_ids and their details
    result_dict = {}
    for idx, row in target_locations.iterrows():
        target_id = row['location_id']
        result_dict[target_id] = {
            'cib_id': row['cib_id'],
            'location_name': row['location_name'],
            'full_address': row['full_address'],
            'lat': row['geometry'].y,
            'long': row['geometry'].x,
            'buyer_overlaps': [],
            'other_overlaps': []
        }

    # Group overlapping buyer locations by target_location_id
    grouped_buyer = overlapping_buyer_locations.groupby('target_location_id')
    for target_id, group in grouped_buyer:
        buyer_locations_list = group[[
            'location_id', 'cib_id', 'location_name', 'full_address', 'lat', 'long'
        ]].to_dict('records')
        result_dict[target_id]['buyer_overlaps'] = buyer_locations_list

    # Group overlapping other locations by target_location_id
    grouped_other = overlapping_other_locations.groupby('target_location_id')
    for target_id, group in grouped_other:
        other_locations_list = group[[
            'location_id', 'cib_id', 'location_name', 'full_address', 'lat', 'long'
        ]].to_dict('records')
        result_dict[target_id]['other_overlaps'] = other_locations_list

    return result_dict



def get_isochrone_analysis(target_location_id, buyer_cib_id, target_cib_id, other_locations, country_id, isochrone_time, tolerance=0.01):
    results_from_db_gdf = geo_queries.get_isochrones_for_locations(other_locations, [buyer_cib_id, target_cib_id], country_id, isochrone_time)

    results_from_db_gdf['type'] = 'other'
    results_from_db_gdf.loc[results_from_db_gdf['cib_id'] == buyer_cib_id, 'type'] = 'buyer'
    results_from_db_gdf.loc[results_from_db_gdf['cib_id'] == target_cib_id, 'type'] = 'target'

    # Simplify geometries with a tolerance (e.g., 0.01 units in the CRS's measurement system)
    results_from_db_gdf['isochrone'] = results_from_db_gdf['isochrone'].simplify(tolerance, preserve_topology=True)


    target_location = results_from_db_gdf[results_from_db_gdf['location_id'] == target_location_id]

    target_isochrone = target_location.iloc[0]['isochrone']
    overlapping_locations = results_from_db_gdf[results_from_db_gdf.geometry.intersects(target_isochrone)]

    market_size = 0.0
    target_market = 0.0
    buyer_market = 0.0
    others_market = 0.0


    for _, row in overlapping_locations.iterrows():
        loc_isochrone = row['isochrone']

        intersection = target_isochrone.intersection(loc_isochrone)
        intersection_fraction = intersection.area / loc_isochrone.area

        market_size += intersection_fraction
        target_market += intersection_fraction if row['type'] == 'target' else 0.0
        buyer_market += intersection_fraction if row['type'] == 'buyer' else 0.0
        others_market += intersection_fraction if row['type'] == 'other' else 0.0


    market_analysis = {
        'market_size': market_size,
        'target_market': target_market,
        'buyer_market': buyer_market,
        'others_market': others_market,
        'standalone_market_share': target_market / market_size if market_size > 0.0 else 0.0,
        'pro_forma_market_share': (target_market + buyer_market) / market_size if market_size > 0.0 else 0.0
    }

    return overlapping_locations, market_analysis


def get_nationwide_market_share(buyer_cib_id, target_cib_id, selected_sectors, other_cib_ids, country_id, target_location_id=None, target_location_radius_km=150):
    locations_by_cib_ids = db.get_locations_by_cib_id_simple([buyer_cib_id, target_cib_id] + other_cib_ids, country_id)

    location_frames = []

    # Fetch locations for the selected sectors
    for sector in selected_sectors:
        location_frames.append(db.get_locations_for_country_sector(country_id, sector))

    # Combine all locations from sectors
    locations_from_sectors = pd.concat(location_frames, ignore_index=True) if location_frames else pd.DataFrame()

    # Combine all location data
    all_locations = pd.concat([locations_by_cib_ids, locations_from_sectors], ignore_index=True)
    all_locations['location_id'] = all_locations['location_id'].astype(str)
    all_locations['area_level_1'] = all_locations['area_level_1'].astype(str)

    all_locations = all_locations.drop_duplicates(subset='location_id')


    # Calculate the number of locations for buyer and target
    buyer_target_locations_count = all_locations[
        all_locations['cib_id'].isin([buyer_cib_id, target_cib_id])
    ].shape[0]

    # Calculate the total number of locations
    total_locations_count = all_locations.shape[0]

    # Calculate the ratio
    ratio = round(buyer_target_locations_count / total_locations_count, 5) if total_locations_count > 0 else 0

    nationwide_result = {'nationwide_buyer_target_locations_count': buyer_target_locations_count,
              'nationwide_total_locations_count': total_locations_count,
              'nationwide_market_share_ratio': ratio}

    region_result = {}
    target_result = {}

    if target_location_id:
        target_location = all_locations[all_locations['location_id'] == target_location_id]

        area = target_location.iloc[0]['area_level_1']

        # get count of buyer and target locations only within this area
        buyer_target_locations_count_area = all_locations[
            (all_locations['area_level_1'] == area) &
            (all_locations['cib_id'].isin([buyer_cib_id, target_cib_id]))
            ].shape[0]

        # get count of all locations within this area
        total_locations_count_area = all_locations[all_locations['area_level_1'] == area].shape[0]

        region_result['region_name'] = area
        region_result['region_buyer_target_locations_count'] = buyer_target_locations_count_area
        region_result['region_total_locations_count'] = total_locations_count_area
        region_result['region_market_share_ratio'] = round(buyer_target_locations_count_area / total_locations_count_area, 5) if total_locations_count_area > 0 else 0

        # result for target market
        filtered_buyer_target = filter_locations_geopandas(all_locations, target_location.iloc[0]['lat'], target_location.iloc[0]['long'], target_location_radius_km, [buyer_cib_id, target_cib_id])
        filtered = filter_locations_geopandas(all_locations, target_location.iloc[0]['lat'], target_location.iloc[0]['long'], target_location_radius_km, all_locations['cib_id'].unique().tolist())

        target_result = {
            'target_buyer_target_locations_count': filtered_buyer_target.shape[0],
            'target_total_locations_count': filtered.shape[0],
            'target_market_share_ratio': round(filtered_buyer_target.shape[0] / filtered.shape[0], 5) if filtered.shape[0] > 0 else 0
        }

    result = {**nationwide_result, **region_result, **target_result}
    return result


# Example usage
#x = get_nationwide_market_share(
#    'valueretail.com', 'haslinger-immobilien.de', [4], ['vestas.com'], 63,
#    target_location_id='cbbbea5a-c38e-43ed-ace5-64da8b3b15db'
#)


def get_buyer_target_overlaps(buyer_cib_id, target_cib_id, distance_m, country_id):
    query = text("""
        select * from get_overlap_by_buyer_target(:buyer_cib_id, :target_cib_id, :distance_m)
        """)

    df = pd.read_sql(
        query,
        db.engine,
        params={
            "buyer_cib_id": buyer_cib_id,
            "target_cib_id": target_cib_id,
            "distance_m": distance_m
        }
    )

    df['region_id'] = df['region_id'].astype(str)
    df['country_id'] = df['country_id'].astype(str)

    # 2) Build your mask:
    mask = (
            (df['region_type'] == 'global')
            | ((df['region_type'] == 'country') & (df['region_id'] == str(country_id)))
            | ((df['region_type'] == 'area_level_1') & (df['country_id'] == str(country_id)))
    )

    # 3) Filter:
    filtered_df = df.loc[mask]

    return filtered_df


import pandas as pd


def transform_overlap_df(df: pd.DataFrame) -> pd.DataFrame:
    """
    Transforms the overlap DataFrame into the required report format.

    Parameters:
        df (pd.DataFrame): input DataFrame with columns:
            - region_type (text)
            - region_id (text or None)
            - within_count (int)
            - total_locations (int)
            - overlap_ratio (float, e.g. 0.4 for 40%)

    Returns:
        pd.DataFrame: formatted report with columns:
            - Column1 (str)
            - Column2 (str)
            - isRowBold (bool)
            - addLineAfterRow (bool)
    """
    rows = []

    # 1) initial blank row
    rows.append({"Column1": "", "Column2": "", "isRowBold": False, "addLineAfterRow": True})
    # 2) header row
    rows.append({"Column1": "Overlap detail", "Column2": "", "isRowBold": True, "addLineAfterRow": False})

    # 3) country row (assume single country entry)
    country = df[df['region_type'] == 'country'].iloc[0]


    country_pct = f"{country['overlap_ratio']:.0%}"
    rows.append({
        "Column1": db.get_country_name(country['region_id']),
        "Column2": country_pct,
        "isRowBold": False,
        "addLineAfterRow": False
    })

    # 4) top 3 area_level_1 rows
    areas = df[df['region_type'] == 'area_level_1'] \
        .sort_values(by='overlap_ratio', ascending=False) \
        .head(3) \
        .reset_index(drop=True)
    for idx, row in areas.iterrows():
        name = f"{row['region_id']}"
        pct = f"{row['overlap_ratio']:.0%}"
        rows.append({
            "Column1": name,
            "Column2": pct,
            "isRowBold": False,
            "addLineAfterRow": False
        })

    # 5) worldwide row
    global_row = df[df['region_type'] == 'global'].iloc[0]
    global_pct = f"{global_row['overlap_ratio']:.0%}"
    rows.append({
        "Column1": "Worldwide",
        "Column2": global_pct,
        "isRowBold": False,
        "addLineAfterRow": True
    })

    # Build final DataFrame
    report_df = pd.DataFrame(rows, columns=["Column1", "Column2", "isRowBold", "addLineAfterRow"])
    return report_df



def get_locations_and_distances(cib_id1, cib_id2, max_dist_km, country_id):
    # PostGIS ST_DWithin works in meters, so convert km→m
    max_dist_m = max_dist_km * 1000

    query = text("""
        SELECT
          t.cib_id AS cib_1,
          t.location_id AS location_1,
          b.cib_id AS cib_2,
            b.location_id AS location_2,
          -- distance in km
          ST_Distance(
            t.coordinates::geography,
            b.coordinates::geography
          ) / 1000.0 AS distance
        FROM locations AS t
        JOIN locations AS b
          ON 1=1
         AND t.cib_id = :cib_id1
         AND b.cib_id = :cib_id2
        WHERE
        ST_DWithin(
            t.coordinates::geography,
            b.coordinates::geography,
            :max_dist_m
          )
            AND t.country_id = :country_id
            AND b.country_id = :country_id;
        """)

    df = pd.read_sql(
        query,
        db.engine,
        params={
            "cib_id1": cib_id1,
            "cib_id2": cib_id2,
            "max_dist_m": max_dist_m,
            "country_id": country_id
        }
    )
    return df


#x = get_buyer_target_overlaps('privatebanking.hsbc.com', 'quirinprivatbank.de', 10000)
#y = transform_overlap_df(x)
#print(y)

def bipartite_min_vertex_cover(G, left_nodes):
    """
    Compute a minimum vertex cover in the bipartite graph G=(L∪R, E),
    given the set of left-side nodes `left_nodes` (L).
    Returns the cover as a set of nodes from G.
    """
    # 1) find a maximum matching
    matching = nx.bipartite.maximum_matching(G, top_nodes=left_nodes)
    L = set(left_nodes)
    R = set(G.nodes()) - L

    # 2) find all free (unmatched) nodes in L
    free = L - set(matching.keys())

    # 3) build the alternating-reachability set Z
    visited = set()
    stack = list(free)
    while stack:
        u = stack.pop()
        if u in visited:
            continue
        visited.add(u)
        if u in L:
            # traverse all non-matching edges u–v
            for v in G[u]:
                if matching.get(u) != v and v not in visited:
                    stack.append(v)
        else:
            # from R, traverse only the matching edge v–u back to L
            v = matching.get(u)
            if v is not None and v not in visited:
                stack.append(v)

    # 4) by König’s theorem, the min-vertex-cover is (L − Z) ∪ (R ∩ Z)
    return (L - visited) | (R & visited)



def get_locations_to_remove_overall(df):
    """
    df must have columns ['location_1','location_2',…].
    Returns the absolute minimum set of locations (either side)
    whose removal breaks every edge.
    """
    G = nx.Graph()
    L = set(df['location_1'])
    for _, row in df.iterrows():
        G.add_edge(row['location_1'], row['location_2'])

    cover = bipartite_min_vertex_cover(G, L)
    return list(cover)


# df = get_locations_and_distances('quirinprivatbank.de', 'privatebanking.hsbc.com', 20)
# #df = get_locations_and_distances("privatebanking.hsbc.com", 'quirinprivatbank.de', 10)
#
# targets, buyers = get_locations_to_remove(df)
# overall = get_locations_to_remove_overall(df)
# print(overall)

#x = get_buyer_target_overlaps('privatebanking.hsbc.com', 'quirinprivatbank.de', 10000, 63)
# x = x[x['region_type']=='country'].iloc[0]['within_count']
# print(x)

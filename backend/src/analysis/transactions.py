import pandas as pd
from sqlalchemy import Engine, text
from sqlalchemy.orm import sessionmaker
from src.data_platform.engine_manager import EngineManager
from src.utils.paths.csv_paths import PATH_TRANSACTIONS
from src.data_platform.DB_connector import get_company_countries_by_locations,get_all_table,get_toplevel_sectors, get_children_sectors
from src.config import transactions_db_settings

engine_manager_transactions: EngineManager = transactions_db_settings.ENGINE_MANAGER

def lite_version_df(df:pd.DataFrame)->pd.DataFrame:
    '''Removes datapoints which are sourced from transcripts, or are calculated using values sourced from transcripts.'''
    cost_detail_cols = ['Overhead','SupplyChain','Manufacturing','COGS','Sales Marketing','customer_service','RealEstate','RD','IT','licensing','ProfessionalServices','Insurance','SGA','Other']
    rev_detail_cols = ['cross_sell','cap_comb','econ_scale','pricing_disc','supplier_access','cobranding','market_adoption','rev_other']
    perc_cols = {'CostSynergiesAbs':'CostSynergiesPerc',
                 'RevenueSynAbs':'RevenueSynPerc',
                 'FundSynAbs':'FundSynergiesPerc',
                 }
    amount_col = {'CostSynergies':'CostSynergiesAbs',
                  'RevenueSynergies':'RevenueSynAbs',
                  'FundingSynergies':'FundSynAbs',
                  'CapexSynergies':'CapexSynAbs',
                  'TaxSynergies':'TaxSynAbs',
                  }
    for col in df.columns:
        lc = col + ' Source Lite'
        if lc in df.columns and col!='TotalSynergiesAbs':
            df[lc].fillna('', inplace=True)
            if col in amount_col: # Yes/No synergies: convert transcript ones to no
                df.loc[(df[lc]=='transcript') & (df[amount_col[col]+' Source Lite']!='valid'),col] = 'no'
            elif col in perc_cols: # For amounts, convert transcript ones to NaN, as well as the corresponding %. If total syn is calculated from it, remove total amount and %.
                df.loc[df[lc]=='transcript',[col,perc_cols[col]]] = pd.NA
                df.loc[(df[lc]=='transcript') & (df['TotalSynergiesAbs Source Lite']!='valid'),['TotalSynergiesAbs','TotalSynergiesPerc']] = pd.NA
            else: # Else, convert transcript ones to NaN
                df[col] = df.apply(lambda row: row[col] if row[lc]!='transcript' else pd.NA, axis=1)
            if col != 'TotalSynergiesAbs' and col not in amount_col:
                df.drop(columns=lc, inplace=True)
    df.loc[(df['TotalSynergiesAbs Source Lite']=='transcript') & (df[list(perc_cols.keys())].apply(lambda x: x.fillna('').astype(str).eq('').all(), axis=1)),['TotalSynergiesAbs','TotalSynergiesPerc']] = pd.NA
    df.loc[df['cost_detail Source Lite']=='transcript',cost_detail_cols] = pd.NA
    df.loc[df['rev_detail Source Lite']=='transcript',rev_detail_cols] = pd.NA
    return df

def get_transaction_ids_by_target(comp:str|None, sector:str|None, subsector_1:str|int, subsector_2:str|int,cont:str|bool=False, lite:bool=False)->list:
    def get_sectors(target:str)->tuple:
        '''Provides sector, subsector_1 and subsector_2 for a given company, if each of these is uniquely defined.'''
        
        sector_df = get_toplevel_sectors(target).sort_values(by='sector_name', ascending=True)
        #if len(sector_df.index) > 1:
        #    print(f'Multiple top level sectors detected: {sector_df['sector_name']}')
        sector = (int(sector_df['sector_id'].iloc[0]),sector_df['sector_name'].iloc[0])

        ss1_df = get_children_sectors(target, sector[0])
        if len(ss1_df.index) != 1:
            #if len(ss1_df.index) > 1:
            #    print(f'Multiple subsector_1: {ss1_df['sector_name'].to_list()}')
            return (sector[1],0,0)
        
        ss1 = (int(ss1_df['sector_id'].iloc[0]),ss1_df['sector_name'].iloc[0])

        ss2_df = get_children_sectors(target, ss1[0])
        if len(ss2_df.index) != 1:
        #    print(f'Multiple subsector_2: {ss2_df['sector_name'].to_list()}')
            return (sector[1],ss1[1],0)
        
        ss2 = (int(ss2_df['sector_id'].iloc[0]),ss2_df['sector_name'].iloc[0])
        return (sector[1],ss1[1],ss2[1])

    def get_target_continent(target:str)->str:
        '''Takes a cib_id and finds the first entry in countries table, so country with the most locations, and then
        finds the continent of the most-locations country. Returns a 2-letter abbreviation'''
        continents = {1:'AF',2:'AS',3:'EU',4:'US',5:'OC',6:'SA'}
        
        # Get country_id
        company_locations = get_company_countries_by_locations(target)
        if company_locations.empty:
            return False
        main_country_id = company_locations.at[0,'country_id']
        
        # Get continent_id
        countries = get_all_table('countries')
        continent_df = countries.loc[countries['country_id']==main_country_id].iloc[0]
        continent_id = continent_df['continent_id']
        return continents[continent_id]

    if comp:
        sectors = get_sectors(comp)
        print(f'Fetched sectors: {sectors}')
        if not(sectors): return []
        cont = get_target_continent(comp)

        sector = sectors[0]
        subsector_1 = sectors[1]
        subsector_2 = sectors[2]

    # Temporary remapping of sectors (for sectors with 2 or less transactions, or different spelled sectors)
    manual_sectors_map = {
        'Home Improvement Retailers':'Diversified Retailers',
        'Institutional Brokerage':'Wealth Management / Brokerage',
        'Oil and Gas':'Power',
        'Other Energy':'Power',
        'P&C and Reinsurance':'Life Insurance',
        'Asset Management':'Asset Management (traditional)',
        'Diversified Financials':'Other Financials',#20250213
        'Telecommunications Equipment':'Electronic Equipment',#20250213
        'Machinery and Equipment':'Machinery and equipment',#20250213
        'Internet & Software Services':'Online Services',#20250221
        'Construction Services':'Construction and Engineering',#20250321
        }
    #if lite:
    #    manual_sectors_map
    if sector in manual_sectors_map:
        sector = manual_sectors_map[sector]

    engine: Engine = engine_manager_transactions.get_or_create()
    Session = sessionmaker(bind=engine)
    session = Session()

    # For sectors that are not present on transaction side, null them (returning a wider set to prevent errors)
    if subsector_1 != 0:
        res = session.execute(text('SELECT DISTINCT subsector_1 FROM sectors'))
        ss1 = [row[0] for row in res]
        if subsector_1 not in ss1:
            subsector_1 = 0
        if subsector_2 != 0:
            res = session.execute(text('SELECT DISTINCT subsector_2 FROM sectors'))
            ss2 = [row[0] for row in res]
            if subsector_2 not in ss2:
                subsector_2 = 0

    level = 10
    while level >= 0:
        # Levels for use with reiterrating
        if level == 10: # If this is the first call, determine the level based on subsector availability.
            if subsector_1 == 0:
                level = 0
            elif subsector_2 == 0:
                level = 1
            else:
                level = 2
        else:
            if level == 0: #Otherwise, remove subsectors based on level.
                subsector_1 = subsector_2 = 0
            elif level == 1:
                subsector_2 = 0
        print(f'Level {level}: Updated sectors and subsectors: {[sector,subsector_1,subsector_2]}\n')

        # Build the query
        query = '''
            SELECT t."DealID",t."AcquirorClearName",t."TargetClearName",t."OrderID",t."Continent",s.sector,s.subsector_1,s.subsector_2,t.syn_quant,t.metrics,t."Year"
            FROM sectors AS s
            JOIN transactions AS t
            ON t."DealID" = s."DealID"
            WHERE s.sector = :sec AND t.syn_quant = 1 AND t.metrics = 1'''
        if lite: query = query.replace('syn_quant','syn_quant_lite').replace('metrics','metrics_lite')
        if subsector_1 != 0:
            query += ' AND s.subsector_1 = :ss1'
            if subsector_2 != 0:
                query += ' AND s.subsector_2 = :ss2'
            else:
                query += ' AND s.subsector_2 IS NULL'
        else:
            query += ' AND s.subsector_1 IS NULL AND s.subsector_2 IS NULL'
        if cont:
            query += '''
                ORDER BY
                    CASE
                        WHEN t."Continent" = :con THEN 0
                        ELSE 1
                    END,
                    t."Year" DESC
                LIMIT 20
            '''
        else:
            query += '''
                ORDER BY t."Year" DESC
                LIMIT 20
            '''
        params = {'sec':sector,'ss1':subsector_1,'ss2':subsector_2,'con':cont}
        tids = session.execute(text(query),params)

        results = [x[0] for x in tids]
        if results:
            return results
        level -= 1
    return results

def get_transactions_by_target(target:str|None, sector:str|None, subsector_1:str|int, subsector_2:str|int,cont:str|bool=False,lite:bool=False):
    tr_ids = get_transaction_ids_by_target(comp=target, sector = sector, subsector_1=subsector_1, subsector_2 = subsector_2, cont=cont, lite=lite)
    df = pd.read_csv(filepath_or_buffer=PATH_TRANSACTIONS)
    df['Year'] = pd.to_numeric(df['Year'])

    if lite: # If lite, use helper columns to remove data which is connected to a transcript
        df = lite_version_df(df)
    existing_ids = [id for id in tr_ids if id in set(df['DealID'])]
    filtered_transactions = df[df['DealID'].isin(tr_ids)].set_index('DealID').loc[existing_ids[:12]].reset_index()
    return filtered_transactions

def transaction_ids_by_sector(sector:str)->list:
    engine: Engine = engine_manager_transactions.get_or_create()
    Session = sessionmaker(bind=engine)
    session = Session()
    res = session.execute(text('SELECT "DealID" FROM sectors WHERE sector=:sec'),{'sec':sector})
    deal_ids = [row[0] for row in res.fetchall()]
    return deal_ids
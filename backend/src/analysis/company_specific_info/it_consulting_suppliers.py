import numpy as np
import pandas as pd
import src.utils.paths.csv_paths as csv_paths


NA_CATEGORY = "NA"

df = pd.read_csv(
    csv_paths.IT_CONSULTING_SUPPLIERS, 
    names=["company", "supplier"], 
    dtype={"company": "category", "supplier": "category"},
    na_values=[NA_CATEGORY],
    na_filter=False,
)

def get_by_acquiror_target(acquiror: str, target: str) -> dict[str, dict[str, list[str]]]:
    df_grouped = df.groupby('company')
    
    try:
        acquiror_group = df_grouped.get_group(acquiror).drop(columns="company")
    except KeyError as e:
        e.args = (f"Acquiror company '{acquiror}' not found.",)
        raise e
    
    try:
        target_group = df_grouped.get_group(target).drop(columns="company")
    except KeyError as e:
        e.args = (f"Target company '{acquiror}' not found.",)
        raise e

    common_suppliers: list = np.intersect1d(acquiror_group["supplier"], target_group["supplier"]).tolist()
    acquiror_only_suppliers: list = np.setdiff1d(acquiror_group["supplier"], target_group["supplier"]).tolist()
    target_only_suppliers: list = np.setdiff1d(target_group["supplier"], acquiror_group["supplier"]).tolist()

    return {
        "it_consulting_suppliers": {
            "common": sorted(common_suppliers, key=str.lower),
            "acquiror_only": sorted(acquiror_only_suppliers, key=str.lower),
            "target_only": sorted(target_only_suppliers, key=str.lower)
        },
    }

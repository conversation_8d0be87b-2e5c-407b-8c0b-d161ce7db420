from typing import Any
import numpy as np
import pandas as pd
import src.utils.paths.csv_paths as csv_paths

pd.options.mode.chained_assignment = None

NA_CATEGORY = "NA"

df = pd.read_csv(
    csv_paths.ECOMMERCE_BRANDS, 
    header=0,
    names=["provider_url", "brand", "items"], 
    dtype={"provider_url": "category", "brand": "string"},
    na_values=[NA_CATEGORY],
    na_filter=False,
)
df["brand"] = df["brand"].str.lower()
df["items"] = df["items"].str.replace(",", "").astype("uint32")


def get_by_acquiror_target(acquiror_url: str, target_url: str, round_to_thousands: bool = True) -> dict[str, dict[str, Any]]:
    if acquiror_url == "" or target_url == "":
        raise ValueError("Acquiror and target URLs must not be empty.")
    
    if acquiror_url == NA_CATEGORY or target_url == NA_CATEGORY:
        raise ValueError(f"Acquiror and target URLs must not be '{NA_CATEGORY}'.")
    
    acquiror_group = df[df["provider_url"] == acquiror_url]
    target_group = df[df["provider_url"] == target_url]

    if acquiror_group.empty:
        raise ValueError(f"Acquiror company URL '{acquiror_url}' not found.")

    if target_group.empty:
        raise ValueError(f"Target company URL '{target_url}' not found.")

    groups = pd.merge(acquiror_group, target_group, on=["brand"], how="outer")
    common_brands = groups.dropna(subset=["provider_url_x", "provider_url_y"])
    common_brands["common_items"] = np.minimum(common_brands["items_x"], common_brands["items_y"])
    top_10_common_brands = common_brands.sort_values(by="common_items", ascending=False).head(10)
    target_only_groups = groups.dropna(subset=["provider_url_y"])
    
    target_no = len(target_only_groups)
    common_no = len(common_brands)
    target_items_sum = int(target_only_groups["items_y"].sum())
    
    if round_to_thousands and target_items_sum > 1000:
        target_items_sum = round(target_items_sum, ndigits=-3)
    
    common_items_sum = int(common_brands["common_items"].sum())
    
    if round_to_thousands and common_items_sum > 1000:
        common_items_sum = round(common_items_sum, ndigits=-3)

    return {
        "brands": {
            "top_10_common": top_10_common_brands["brand"].tolist(),
            "target_no": target_no,
            "common_no": common_no,
            "common_perc": int(common_no / target_no * 100),
            "target_items_sum": target_items_sum,
            "target_items_sum_for_common_brands": common_items_sum,
            "target_items_sum_for_common_brands_perc": int(common_items_sum / target_items_sum * 100),
        },
    }

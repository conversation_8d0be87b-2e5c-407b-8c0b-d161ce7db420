import numpy as np
import pandas as pd
import src.utils.paths.csv_paths as csv_paths


NA_CATEGORY = "NA"

df = pd.read_csv(
    csv_paths.CARS,
    names=["company_url", "brand", "oem"], 
    dtype={"company_url": "category", "brand": "category", "oem": "category"},
    na_values=[NA_CATEGORY],
    na_filter=False,
    sep=";"
)


def get_by_acquiror_target(acquiror_url: str, target_url: str) -> dict[str, dict[str, list[str]]]:
    df_grouped = df.groupby('company_url')

    try:
        acquiror_group = df_grouped.get_group(acquiror_url).drop(columns="company_url")
    except KeyError as e:
        e.args = (f"Acquiror company URL '{acquiror_url}' not found.",)
        raise e

    try:
        target_group = df_grouped.get_group(target_url).drop(columns="company_url")
    except KeyError as e:
        e.args = (f"Target company URL '{target_url}' not found.",)
        raise e

    common_brands: list = np.intersect1d(acquiror_group["brand"], target_group["brand"]).tolist()
    acquiror_only_brands: list = np.setdiff1d(acquiror_group["brand"], target_group["brand"]).tolist()
    target_only_brands: list = np.setdiff1d(target_group["brand"], acquiror_group["brand"]).tolist()

    common_oems: list = np.intersect1d(acquiror_group["oem"], target_group["oem"]).tolist()
    acquiror_only_oems: list = np.setdiff1d(acquiror_group["oem"], target_group["oem"]).tolist()
    target_only_oems: list = np.setdiff1d(target_group["oem"], acquiror_group["oem"]).tolist()

    return {
        "brands": {
            "common": sorted(common_brands, key=str.lower),
            "acquiror_only": sorted(acquiror_only_brands, key=str.lower),
            "target_only": sorted(target_only_brands, key=str.lower),
        },
        "oem": {
            "common": sorted(common_oems, key=str.lower),
            "acquiror_only": sorted(acquiror_only_oems, key=str.lower),
            "target_only": sorted(target_only_oems, key=str.lower),
        }
    }

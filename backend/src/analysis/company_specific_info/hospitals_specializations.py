from typing import Any
import numpy as np
import pandas as pd
import src.utils.paths.csv_paths as csv_paths

pd.options.mode.chained_assignment = None

NA_CATEGORY = "NA"
ID_COLUMN = "cib_id"
GROUP_COLUMN = "specialization"
VALUE_COLUMN = "value"

df = pd.read_csv(
    csv_paths.HOSPITALS_SPECIALIZATIONS, 
    names=[ID_COLUMN, GROUP_COLUMN, VALUE_COLUMN],
    header=0,
    dtype={ID_COLUMN: "category", GROUP_COLUMN: "category"},
    na_values=[NA_CATEGORY],
    na_filter=False,
)
df[ID_COLUMN] = df[ID_COLUMN].str.lower()

df_large = pd.read_csv(
    csv_paths.HOSPITALS_SPECIALIZATIONS_LARGE, 
    usecols=[0, 1, 2, 3, 7], 
    na_filter=False,
)
df_large["Buyer"] = df_large["Buyer"].str.lower()
df_large["Target"] = df_large["Target"].str.lower()


def get_by_acquiror_target(acquiror_url: str, target_url: str) -> dict[str, Any]:
    if acquiror_url == "" or target_url == "":
        raise ValueError("Acquiror and target URLs must not be empty.")
    
    if acquiror_url == NA_CATEGORY or target_url == NA_CATEGORY:
        raise ValueError(f"Acquiror and target URLs must not be '{NA_CATEGORY}'.")
    
    acquiror_group = df[df[ID_COLUMN] == acquiror_url]
    target_group = df[df[ID_COLUMN] == target_url]

    if acquiror_group.empty:
        raise ValueError(f"Acquiror company URL '{acquiror_url}' not found.")

    if target_group.empty:
        raise ValueError(f"Target company URL '{target_url}' not found.")

    acquiror_only: list = np.setdiff1d(acquiror_group[GROUP_COLUMN], target_group[GROUP_COLUMN]).tolist()
    target_only: list = np.setdiff1d(target_group[GROUP_COLUMN], acquiror_group[GROUP_COLUMN]).tolist()

    groups = pd.merge(acquiror_group, target_group, on=[GROUP_COLUMN], how="outer")
    common_value = groups.dropna(subset=[f"{ID_COLUMN}_x", f"{ID_COLUMN}_y"])
    common_brands_sorted = common_value.sort_values(by=f"{VALUE_COLUMN}_y", ascending=False)
    target_only_groups = groups.dropna(subset=[f"{VALUE_COLUMN}_y"])

    target_no = len(target_only_groups)
    common_no = len(common_value)

    bed_capacity_overlap: float = df_large[(df_large["Buyer"] == acquiror_url) & (df_large["Target"] == target_url)]["Sum of Matched Target Values"].tolist()[0]

    return {
        "common": common_brands_sorted[GROUP_COLUMN].tolist(), 
        "acquiror_only": sorted(acquiror_only, key=str.lower), 
        "target_only": sorted(target_only, key=str.lower), 
        "target_no": target_no,
        "common_no": common_no,
        "common_perc": int(common_no / target_no * 100),
        "bed_capacity_overlap": bed_capacity_overlap,
    }

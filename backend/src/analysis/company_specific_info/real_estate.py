import pandas as pd
import src.utils.paths.csv_paths as csv_paths


real_estate_cities = pd.read_csv(csv_paths.PATH_REAL_ESTATE_CITIES, sep=",")
real_estate_notes = pd.read_csv(csv_paths.PATH_REAL_ESTATE_NOTES, sep=",")


def get_by_buyer_and_target(buyer_cib_id, target_cib_id=None):
    df = real_estate_cities

    df['Radius_KM'] = df['Radius_KM'].astype(str)
    df["Lat"] = df['Lat'].astype(str)
    df["Long"] = df['Long'].astype(str)

    # If no target is provided, just filter on buyer and empty/NA target.
    if target_cib_id is None or target_cib_id == "":
        buyer_filter = df['Buyer'].str.contains(buyer_cib_id, case=False, na=False)
        target_filter = df['Target'].isna() | (df['Target'] == "")
        return df[buyer_filter & target_filter].copy()

    # Two cases when both IDs are provided:
    # Case 1: The provided buyer_cib_id is in the Buyer column and target_cib_id in the Target column.
    normal_order = (
        df['Buyer'].str.contains(buyer_cib_id, case=False, na=False) &
        df['Target'].str.contains(target_cib_id, case=False, na=False)
    )

    # Case 2: The provided buyer_cib_id is in the Target column and target_cib_id in the Buyer column.
    # rotated_order = (
    #     df['Buyer'].str.contains(target_cib_id, case=False, na=False) &
    #     df['Target'].str.contains(buyer_cib_id, case=False, na=False)
    # )

    # Get the rows for both cases.
    result_normal = df[normal_order].copy()
    #result_rotated = df[rotated_order].copy()

    # For the rotated rows, swap the Buyer and Target columns.
    # if not result_rotated.empty:
    #     result_rotated[['Buyer', 'Target']] = result_rotated[['Target', 'Buyer']]

    # Combine the results.
#    return pd.concat([result_normal, result_rotated])
    return result_normal



def has_real_estate_data(cib_id):
    b = bool(real_estate_cities['Buyer'].str.contains(cib_id, case=False, na=False).any())
    t = bool(real_estate_cities['Target'].str.contains(cib_id, case=False, na=False).any())

    return b or t

def get_notes(buyer_cib_id, target_cib_id=None):
    # Create a case-insensitive filter for the Buyer column
    buyer_filter = real_estate_notes['Buyer'].str.contains(buyer_cib_id, case=False, na=False)

    target_filter = real_estate_notes['Target'].str.contains(target_cib_id, case=False, na=False)

    print(real_estate_notes[buyer_filter & target_filter])

    return real_estate_notes[buyer_filter & target_filter]


#x = get_by_buyer_and_target('vonovia.com', 'tag-ag.com')
#print(x)
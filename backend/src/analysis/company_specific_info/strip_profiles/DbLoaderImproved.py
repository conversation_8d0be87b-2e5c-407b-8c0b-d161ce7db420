from abc import ABC, abstractmethod
import pandas as pd
from pathlib import Path

from sqlalchemy import create_engine, text, inspect

from src.config import markup_db_settings

from src.analysis.company_specific_info.strip_profiles.strip_profile_loader import Loader

class DbLoaderImproved(Loader):
    def __init__(self) -> None:
        print("Will load strip profiles directly from manual_strip_profiles.")

    def get_columns_for_cib_ids(self, cib_ids, columns=None):
        engine = markup_db_settings.ENGINE_MANAGER.get_or_create()
        #inspector = inspect(engine)

        if not columns:
            with engine.connect() as conn:
                query_columns = set(
                    row[0]
                    for row in conn.execute(
                        text("SELECT DISTINCT property FROM manual_strip_profiles WHERE cib_id = ANY(:cib_ids)"),
                        {"cib_ids": cib_ids}
                    )
                )
                # when no data is added manually we still need some columns, this needs to be fixed
            filtered_props = query_columns.union(set(['total_employees', 'total_employees_source_complete']))
        else:
            #filtered_props = [col for col in columns if col in query_columns]
            filtered_props = columns
            if not filtered_props:
                return pd.DataFrame({"cib_id": cib_ids})  # fallback empty

        query = """
            SELECT * FROM get_remapped_strip_profiles2(
                :cib_ids,
                :properties
            )
        """

        df_raw = pd.read_sql(text(query), engine, params={
            "cib_ids": cib_ids,
            "properties": list(filtered_props)
        })

        try:
            df = df_raw.pivot(index="cib_id", columns="property", values="value").reset_index()
        except ValueError as e:

            # important to keep this report as data errors happen quite often and it makes them easier to debug

            msg = str(e)
            if "duplicate entries" in msg:
                # Build a concise duplicate report only on failure
                dupes = (
                    df_raw.value_counts(["cib_id", "property"])
                    .loc[lambda s: s > 1]
                    .reset_index(name="count")
                    .sort_values(["count", "cib_id", "property"], ascending=[False, True, True])
                )
                samples = (
                    df_raw.merge(dupes[["cib_id", "property"]], on=["cib_id", "property"], how="inner")
                    .sort_values(["cib_id", "property"])
                    .head(20)  # trim to avoid huge messages
                )
                raise ValueError(
                    "Duplicate (cib_id, property) pairs prevent pivot:\n\n"
                    f"{dupes.to_string(index=False)}\n\n"
                    "Sample duplicate rows:\n"
                    f"{samples.to_string(index=False)}"
                ) from e
            else:
                raise

        return df

    def get_available_cib_ids(self, all_cib_ids):
        query = text("""
            SELECT DISTINCT cib_id
            FROM (
                SELECT cib_id AS cib_id
                FROM manual_strip_profiles
                WHERE cib_id = ANY(:cib_ids)
            
                UNION
            
                SELECT cib_id
                FROM total_employees_consolidated_3
                WHERE cib_id = ANY(:cib_ids)
            )
        """)
        engine = markup_db_settings.ENGINE_MANAGER.get_or_create()
        df = pd.read_sql(query, engine, params={"cib_ids": all_cib_ids})
        return df['cib_id'].tolist()


#x = DbLoaderImproved()
#y = x.get_columns_for_cib_ids(['st-tettau.gmbh'])
#print(y.columns)

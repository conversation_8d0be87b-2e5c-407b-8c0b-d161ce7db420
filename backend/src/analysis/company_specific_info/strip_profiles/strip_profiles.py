from typing import Annotated, Any

import pandas as pd
from src.analysis.company_specific_info.strip_profiles.strip_profile_loader import Loader, <PERSON>bLoader, CsvLoader
import src.utils.paths.csv_paths as csv_paths
import src.analysis.company_specific_info.strip_profiles.DbLoaderImproved as DbLoaderImproved
import src.data_platform.DB_connector as DB
from . import map_zoom


class LoaderSingleton:
    _instance: Loader = None

    @classmethod
    def get_instance(cls) -> Loader:
        if cls._instance is None:
            #cls._instance = CsvLoader(Path(csv_paths.STRIP_PROFILES))
            #print("Loaded strip profiles from CSV")
            #cls._instance = DbLoader()
            #print("Loaded strip profiles from DB")

            cls._instance = DbLoaderImproved.DbLoaderImproved()
            print("Loaded strip profiles from DB (improved)")

        return cls._instance


def get_loader() -> Loader:
    return LoaderSingleton.get_instance()


#def get_loader():
#    return loader


def get_strip_profiles_map(cib_ids: list[str]) -> pd.DataFrame:
    df = DB.get_companies_with_countries(cib_ids)
    result: list[dict] = []

    for _, row in df.iterrows():
        _cib_id: str = row['cib_id']
        _company_name: str = row['company_name']
        _countries: list[str] = row['country_list']
        _single_result = map_zoom.compute_zoom(cib_id=_cib_id, company_name=_company_name, countries=_countries)
        result.append(_single_result)
    return pd.DataFrame(result)


def get_available_cib_ids_with_strip_profiles(all_cib_ids):
    """
    Returns a list of CIB IDs that have strip profiles.
    """
    # strip_profiles = get_strip_profiles()
    # available_cib_ids = strip_profiles.index.tolist()
    # return [cib_id for cib_id in all_cib_ids if cib_id in available_cib_ids]

    return get_loader().get_available_cib_ids(all_cib_ids)



from src.analysis.company_specific_info.strip_profiles.panels.StripProfilePanel import StripProfilePanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_tax_advisors import TaxAdvisorPanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_wm import WealthManagementPanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_sample_sector import SampleSectorPanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_pharma_services import PharmaServicesPanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_construction_services import ConstructionServicesPanel
from src.analysis.company_specific_info.strip_profiles.panels.panel_material_testing import MaterialTestingPanel

def create_panel_for_sector(sector_ids) -> StripProfilePanel:

    mappings = {153: TaxAdvisorPanel(),
                41: WealthManagementPanel(),
                156: SampleSectorPanel(),
                157: PharmaServicesPanel(include_services=True), # pharma services
                158: PharmaServicesPanel(include_services=True), # commercialization
                159: PharmaServicesPanel(include_services=False), # packaging and distribution
                160: PharmaServicesPanel(include_services=True, include_client_focus=False), # cdmo
                18: PharmaServicesPanel(include_services=False, include_client_focus=False), # dyalisys
                123: PharmaServicesPanel(include_services=False, include_client_focus=False), # dyalisys
                164: PharmaServicesPanel(include_services=False, include_client_focus=False), # lab
                124: PharmaServicesPanel(include_client_focus=False), # clinics and acute care
                126: PharmaServicesPanel(include_services=False, include_client_focus=False), # dyalisys
                127: PharmaServicesPanel(include_client_focus=False), # rehabilitation and therapy
                129: PharmaServicesPanel(include_services=False, include_client_focus=False), # veterinary clinics
                147: PharmaServicesPanel(include_services=False, include_client_focus=False), # wellness
                31: PharmaServicesPanel(include_services=False, include_client_focus=False), # dyalisys
                23: ConstructionServicesPanel(),
                163: ConstructionServicesPanel(),
                166: MaterialTestingPanel(),
                25: PharmaServicesPanel(include_services=False, include_client_focus=False),  # Medical Equipment and Supplies same as Laboratories
                }

    for sector_id in sector_ids:
        if sector_id in mappings.keys():
            return mappings[sector_id]

    raise ValueError(f"No strip profile panel available for sector IDs {sector_ids}")
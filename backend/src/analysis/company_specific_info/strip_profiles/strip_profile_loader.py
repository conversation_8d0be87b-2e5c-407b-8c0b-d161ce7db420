from abc import ABC, abstractmethod
import pandas as pd
from pathlib import Path

from sqlalchemy import create_engine, text, inspect

from src.config import markup_db_settings



class Loader(ABC):
    @abstractmethod
    def get_columns_for_cib_ids(self, cib_ids, columns): ...

    @abstractmethod
    def get_available_cib_ids(self, all_cib_ids): ...


class CsvLoader(Loader):
    def __init__(self, data_filepath: Path) -> None:
        if not data_filepath.exists():
            raise FileNotFoundError(f"Error: CSV file not found: {data_filepath}")
        
        self.data_filepath: Path = data_filepath
        self.df = self._load()
    
    def _load(self) -> pd.DataFrame:
        df = pd.read_csv(self.data_filepath, sep=';')
        df.set_index('cib_id', inplace=True)
        print(f"Loaded {len(df)} rows from {self.data_filepath}")
        return df

    def get_columns_for_cib_ids(self, cib_ids, columns = None):
        #df = self.load()
        df = self.df
        df = df[~df.index.duplicated(keep='first')]

        if columns is None:
            return df.reindex(cib_ids).reset_index().rename(columns={"index": "cib_id"})

        df = df.reindex(cib_ids)[columns].reset_index().rename(columns={"index": "cib_id"})
        return df

    def get_available_cib_ids(self, all_cib_ids):
        #df = self.load()
        df = self.df
        available_cib_ids = df.index.tolist()
        return [cib_id for cib_id in all_cib_ids if cib_id in available_cib_ids]


# can be removed at some point
class DbLoader(Loader):
    def __init__(self) -> None:
        print("Will load strip profiles from DB.")

    def get_columns_for_cib_ids(self, cib_ids, columns=None):
        engine = markup_db_settings.ENGINE_MANAGER.get_or_create()

        # Reflect the table to get valid column names
        inspector = inspect(engine)
        valid_columns = set(c['name'] for c in inspector.get_columns("manual_strip_profiles_latest_expanded"))

        # Always include cib_id for filtering
        if not columns:
            columns_clause = '*'
        else:
            # Filter out invalid columns
            safe_columns = [col for col in columns if col in valid_columns]
            if not safe_columns:
                columns_clause = 'cib_id'  # fallback to just cib_id if no valid columns remain
            else:
                quoted_columns = ', '.join(f'"{col}"' for col in safe_columns)
                columns_clause = f'cib_id, {quoted_columns}'

        query = f"""
            SELECT {columns_clause}
            FROM manual_strip_profiles_latest_expanded
            WHERE LOWER(cib_id) = ANY(:cib_ids)
        """
        lowercase_cib_ids = [cib_id.lower() for cib_id in cib_ids]

        df = pd.read_sql(text(query), engine, params={"cib_ids": lowercase_cib_ids})
        return df

    def get_available_cib_ids(self, all_cib_ids):
        lowercase_cib_ids = [cib_id.lower() for cib_id in all_cib_ids]

        query = text("""
            SELECT DISTINCT cib_id
            FROM manual_strip_profiles_latest_expanded
            WHERE LOWER(cib_id) = ANY(:cib_ids)
        """)
        engine = markup_db_settings.ENGINE_MANAGER.get_or_create()
        df = pd.read_sql(query, engine, params={"cib_ids": lowercase_cib_ids})
        return df['cib_id'].tolist()



# x = DbLoader()
# y = x.get_columns_for_cib_ids(['quirinprivatbank.de'], ['total_employees'])
# print(y)

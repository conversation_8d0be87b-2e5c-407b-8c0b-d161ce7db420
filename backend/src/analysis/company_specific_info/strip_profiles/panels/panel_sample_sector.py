from collections import defaultdict
from typing import Annotated, Any, DefaultDict
import pandas as pd

import src.analysis.company_specific_info.strip_profiles.panels.StripProfilePanel as StripProfilePanel


super_cls = StripProfilePanel.StripProfilePanel


class SampleSectorPanel(super_cls):

    def format_row_for_target_search(self,
        data: Annotated[dict[str, Any], "Single row of the main dataframe with all columns"],
        n_locations: int,
        locations_source: str,
        company_name: str
    ) -> pd.DataFrame:
        if data == {}:
            return pd.DataFrame()

        columns: list[str] = ['Column1', 'Column2', 'rowSource', 'addLineAfterRow']
        service_column_values: list[str] = ["SV1", "SV2", "SV3", "SV4", "SV5", "Other"]
        nan_str: str = "N/A"
        empty_str: str = ""
        data_standardized: dict[str, Any] = {k: (nan_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d: DefaultDict = defaultdict(lambda: nan_str, data_standardized)
        data_standardized_empty: dict[str, Any] = {k: (empty_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d_empty: DefaultDict = defaultdict(lambda: empty_str, data_standardized_empty)

        has_cagr: bool = not self.is_empty_like(d["total_employees_cagr"])

        # Define checkbox values: "true" for "yes", "false" for "no", "disabled" otherwise
        # Normalize and collect values for inspection
        normalized_values = [
            str(d.get(key, "")).strip().lower() for key in service_column_values
        ]

        # Check if all values are empty or equivalent to "na"
        all_disabled = all(val in {"", "na", "n/a"} for val in normalized_values)

        # Build checkbox values accordingly
        if all_disabled:
            checkbox_values = ["disabled"] * len(service_column_values)
        else:
            checkbox_values = [
                "true" if val == "yes" else "false" for val in normalized_values
            ]


        # payroll

        # Check payroll value and determine if it should be disabled
        payroll_value = str(d.get("payroll_focus", "")).strip().lower()
        payroll_checkbox = 'CHECKBOX:disabled' if payroll_value in {"", "na", "n/a"} else (
            'CHECKBOX:true' if payroll_value == "yes" else 'CHECKBOX:false')

        # Check if payroll source has content
        payroll_source = d_empty["payroll_focus_source_complete"]
        payroll_source_to_display = payroll_source if payroll_source.strip() else ""


        # Construct the combined HTML description from source fields
        source_fields = [
            d_empty.get("SV1_source_complete", ""),
            d_empty.get("SV2_source_complete", ""),
            d_empty.get("SV3_source_complete", ""),
            d_empty.get("SV4_source_complete", ""),
            d_empty.get("SV5_source_complete", ""),
            d_empty.get("Other_source_complete", ""),
        ]

        # Check if any source field has content
        has_any_source = any(field.strip() for field in source_fields)

        # Only join sources if at least one has content, otherwise use empty string
        source_description = "<br><br>".join(source_fields) if has_any_source else ""

        checkbox_values_result = (
            '<b>Services advertised:</b>',
            'CHECKBOX:' + ','.join(checkbox_values),
            source_description,
            False
        )

        result_list: list[tuple] = [
            (company_name, None, empty_str, True),
            ('<b>Total employees (#)</b>', f'<b>{self.format_number_string(d.get("total_employees", "N/A"))}</b>', d_empty["total_employees_source_complete"], False),
            ('<i>CAGR (last 3 years)</i>', f'<i>{100 * float(d["total_employees_cagr"].strip("%").replace(",", ".")):+.1f}%</i>' if has_cagr else nan_str, d_empty["total_employees_cagr_source"], False),
            ('<b>Front-end employees (#)</b>', f'<b>{self.format_number_string(d.get("total_front_end_employees", "N/A"))}</b>', d_empty["total_front_end_employees_source_complete"], False),
            ('<b>Locations (#)</b>', f'<b>{n_locations}</b>', locations_source, True),
            ('<b>Client focus:</b>', d["client_focus"], d_empty["client_focus_source_complete"], False),
            ('<b>Services:</b>', '; '.join(d["product_summary_noverb"].split(', ')), d_empty["product_summary_source_complete"], False),
            #main_checkbox_row,
            (None, '\u00A0&nbsp;\u00A0'.join(key for key in service_column_values), empty_str, False),
            #('<b>Services advertised:</b>', 'CHECKBOX:' + ','.join('true' if str(d[key]).lower() == "yes" else 'false' for key in service_column_values), d_empty["Stb_source_complete"] + "<br><br>" + d_empty['WP_source_complete'] + "<br><br>" +d_empty['RA_source_complete'] +"<br><br>" + d_empty['notary_source_complete'] +"<br><br>" + d_empty['UB_source_complete'] +"<br><br>" + d_empty['Other_source_complete'], False),
            checkbox_values_result,
            ('<b>Ownership type:</b>', d["ownership_detail"], d_empty["ownership_detail_source_complete"], False),
            ('<b>Ownership detail:</b>', '; '.join(str(d["ownership_type"]).split('; ')), d_empty["ownership_type_source_complete"], False),
            ('<b>Year founded:</b>', self.format_number_string(d.get("firm_years_summary", "N/A")).replace(",",""), d_empty["firm_years_summary_source_complete"], False),
            ('<b>Website:</b>', self.make_link(d["url"], data['cib_id']), empty_str, True),
        ]
        has_revenue: bool = not self.is_empty_like(d["revenue"])
        has_operating_profit: bool = not self.is_empty_like(d["operating_profit"])

        if has_revenue:
            result_list.append(('<b>Revenue:</b>', f'<b>€ {float(d["revenue"].replace(",", ".")):+.1f} m</b>', d_empty["revenue_source_complete"], False))

        if has_operating_profit and not self.is_empty_like(d["operating_profit_cagr"]):
            result_list.append(('<i>CAGR (last 3 years)</i>', f'<i>{100 * float(d["operating_profit_cagr"].strip("%").replace(",", ".")):+.1f}%</i>', d_empty["operating_profit_cagr_source_complete"], False))

        result = pd.DataFrame(data=result_list, columns=columns)
        return result
from typing import Annotated, Any
import pandas as pd

class StripProfilePanel:

    def make_link(self, url, default):
        if url == "" or url is None or url == "N/A" or url == "www.N/A":
            url = default
        res = f'<a href=https://{url} target="_blank" rel="noopener noreferrer"><u>{url}</u></a>'
        return res

    def format_number_string(self, value, num_decimals=2) -> str:
        """
        Format a string or number as a number with thousands separators.

        - If the input is an empty string, 'na', or 'n/a' (case-insensitive), return 'N/A'.
        - Otherwise, try to parse it as a float or int and return with commas.
        """
        value_str = str(value).strip().lower()

        if value_str in {"", "na", "n/a"}:
            return "N/A"

        try:
            num = float(value_str.replace(",", ""))
            if num.is_integer():
                return f"{int(num):,}"
            else:
                return f"{num:,.{num_decimals}f}"
        except ValueError:
            return "N/A"


    def is_empty_like(self, a: Any | None) -> bool:
        result: bool = a is None \
          or pd.isna(a) \
          or a == 'N/A' \
           or a == 'Unknown' \
           or not str(a)
        return result


    def format_row_for_target_search(self,
        data: Annotated[dict[str, Any], "Single row of the main dataframe with all columns"],
        n_locations: int,
        locations_source: str,
        company_name: str
    ) -> pd.DataFrame:
        raise NotImplementedError


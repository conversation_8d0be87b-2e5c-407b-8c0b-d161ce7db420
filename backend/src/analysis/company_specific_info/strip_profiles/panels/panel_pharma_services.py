from collections import defaultdict
from typing import Annotated, Any, DefaultDict
import pandas as pd

import src.analysis.company_specific_info.strip_profiles.panels.StripProfilePanel as StripProfilePanel
super_cls = StripProfilePanel.StripProfilePanel

class PharmaServicesPanel(super_cls):

    def __init__(self, include_services=True, include_client_focus=True):
        super().__init__()
        self.include_services = include_services
        self.include_client_focus = include_client_focus

    def format_row_for_target_search(self,
        data: Annotated[dict[str, Any], "Single row of the main dataframe with all columns"],
        n_locations: int,
        locations_source: str,
        company_name: str
    ) -> pd.DataFrame:
        if data == {}:
            return pd.DataFrame()

        columns: list[str] = ['Column1', 'Column2', 'rowSource', 'addLineAfterRow']
        service_column_titles: list[str] = ["CRO", "Data", "PV", "MA", "Comm.", "Strat.", "Oth."]
        service_column_values: list[str] = ["cro", "data", "pv", "nt", "comm", "strategy", "other"]
        nan_str: str = "N/A"
        empty_str: str = ""
        data_standardized: dict[str, Any] = {k: (nan_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d: DefaultDict = defaultdict(lambda: nan_str, data_standardized)
        data_standardized_empty: dict[str, Any] = {k: (empty_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d_empty: DefaultDict = defaultdict(lambda: empty_str, data_standardized_empty)

        # Define checkbox values: "true" for "yes", "false" for "no", "disabled" otherwise
        # Normalize and collect values for inspection
        normalized_values = [
            str(d.get(key, "")).strip().lower() for key in service_column_values
        ]

        # Check if all values are empty or equivalent to "na"
        all_disabled = all(val in {"", "na", "n/a"} for val in normalized_values)

        # Build checkbox values accordingly
        if all_disabled:
            checkbox_values = ["disabled"] * len(service_column_values)
        else:
            checkbox_values = [
                "true" if val == "yes" else "false" for val in normalized_values
            ]


        # Construct the combined HTML description from source fields
        source_fields = [
            d_empty.get("cro_source_complete", ""),
            d_empty.get("data_source_complete", ""),
            d_empty.get("pv_source_complete", ""),
            d_empty.get("ma_source_complete", ""),
            d_empty.get("comm_source_complete", ""),
            d_empty.get("strategy_source_complete", ""),
            d_empty.get("other_source_complete", "")
        ]

        # Check if any source field has content
        has_any_source = any(field.strip() for field in source_fields)

        # Only join sources if at least one has content, otherwise use empty string
        source_description = "<br><br>".join(source_fields) if has_any_source else ""

        checkbox_values_result = (
            '<b>Services advertised:</b>',
            'CHECKBOX:' + ','.join(checkbox_values),
            source_description,
            False
        )

        website_url = d["Website"] if d["Website"] else d["url"]
        website_url = website_url if website_url.startswith("www.") else "www." + website_url

        result_list: list[tuple] = [
            (company_name, None, empty_str, True),
            ('<b>Total employees (#)</b>', f'<b>{self.format_number_string(d.get("total_employees", "N/A"))}</b>', d_empty["total_employees_source_complete"], False),
            ('<b>Locations (#)</b>', f'<b>{n_locations}</b>', locations_source, True),
            ('<b>Services:</b>', '; '.join(d["product_summary_noverb"].split(', ')), d_empty["product_summary_source_complete"], False),
            ('<b>Ownership type:</b>', d["ownership_type"], d_empty["ownership_type_source_complete"], False),
            ('<b>Ownership detail:</b>', '; '.join(str(d["ownership_detail"]).split('; ')), d_empty["ownership_detail_source_complete"], False),
            ('<b>Year founded:</b>', self.format_number_string(d.get("firm_years_summary", "N/A")).replace(",",""), d_empty["firm_years_summary_source_complete"], False),
            ('<b>Website:</b>', self.make_link(website_url, data['cib_id']), empty_str, True),
        ]

        if self.include_client_focus:
            new_list = [
                ('<b>Client focus:</b>', d["client_summary_noverb"], d_empty["product_summary_source_complete"], False),
            ]

            result_list = result_list[:3] + new_list + result_list[3:]

        if self.include_services:
            new_list = [
            (None, '\u00A0&nbsp;'.join(key for key in service_column_titles), empty_str, False) ,
            checkbox_values_result ,
            ]

            # add new list after position 5 in the original list
            result_list = result_list[:5] + new_list + result_list[5:]

        has_revenue: bool = not self.is_empty_like(d["revenue"])

        if has_revenue:
            result_list.append(('<b>Revenue:</b>', f'<b>€ {float(d["revenue"].replace(",", ".")):+.1f} m</b>', d_empty["revenue_source_complete"], False))

        result = pd.DataFrame(data=result_list, columns=columns)
        return result
from collections import defaultdict
from typing import Annotated, Any, DefaultDict
import pandas as pd

import src.analysis.company_specific_info.strip_profiles.panels.StripProfilePanel as StripProfilePanel


super_cls = StripProfilePanel.StripProfilePanel

class WealthManagementPanel(super_cls):

    def format_row_for_target_search(self,
        data: Annotated[dict[str, Any], "Single row of the main dataframe with all columns"],
        n_locations: int,
        locations_source: str,
        company_name: str
    ) -> pd.DataFrame:
        if data == {}:
            return pd.DataFrame()

        columns: list[str] = ['Column1', 'Column2', 'rowSource', 'addLineAfterRow']
        nan_str: str = "N/A"
        empty_str: str = ""
        data_standardized: dict[str, Any] = {k: (nan_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d: DefaultDict = defaultdict(lambda: nan_str, data_standardized)
        data_standardized_empty: dict[str, Any] = {k: (empty_str if self.is_empty_like(v) else v) for k, v in data.items()}
        d_empty: DefaultDict = defaultdict(lambda: empty_str, data_standardized_empty)


        service_column_values: list[str] = ["ADV", "BROK", "FO", "RP", "EP", "Other"]

        # Define checkbox values: "true" for "yes", "false" for "no", "disabled" otherwise
        # Normalize and collect values for inspection
        normalized_values = [
            str(d.get(key.lower(), "")).strip().lower() for key in service_column_values
        ]


        # Check if all values are empty or equivalent to "na"
        all_disabled = all(val in {"", "na", "n/a"} for val in normalized_values)

        # Build checkbox values accordingly
        if all_disabled:
            checkbox_values = ["disabled"] * len(service_column_values)
        else:
            checkbox_values = [
                "true" if val == "yes" else "false" for val in normalized_values
            ]

        # Construct the combined HTML description from source fields
        source_fields = [
            d_empty.get("adv_source_complete", ""),
            d_empty.get("brok_source_complete", ""),
            d_empty.get("fo_source_complete", ""),
            d_empty.get("rp_source_complete", ""),
            d_empty.get("ep_source_complete", ""),
            d_empty.get("other_source_complete", "")
        ]

        # Check if any source field has content
        has_any_source = any(field.strip() for field in source_fields)

        # Only join sources if at least one has content, otherwise use empty string
        source_description = "<br><br>".join(source_fields) if has_any_source else ""

        checkbox_values_result = (
            '<b>Services advertised:</b>',
            'CHECKBOX:' + ','.join(checkbox_values),
            source_description,
            False
        )

        international_locations = d.get("international_locations") if d.get("international_locations") != 'No' else "0"
        international_clients = self.format_number_string(d.get("international_clients", "N/A"))

        website_url = d["Website"] if d["Website"] else d["url"]
        website_url = website_url if website_url.startswith("www.") else "www." + website_url

        result_list: list[tuple] = [
            (company_name, None, empty_str, True),
            ('<b>AUM ($bn)</b>', f'<b>{self.format_number_string(d.get("AUM", "N/A"), num_decimals=1)}</b>', d_empty["AUM_source_complete"], False),
            ('<b>Avg. AUM / client ($mm)</b>', f'<b>{self.format_number_string(d.get("aum_per_client", "N/A"), num_decimals=1)}</b>', d_empty["AUM_source_complete"], True),
            ('<b>Clients (#)</b>', f'<b>{self.format_number_string(d.get("num_clients", "N/A"))}</b>', d_empty["num_clients_source_complete"], False),
            ('<b>Share of intl. clients (%)</b>', f'<b>{international_clients + "%" if international_clients != "N/A" else "N/A"}</b>', d_empty["international_clients_source_complete"], True),
            ('<b>Total employees</b>', f'<b>{self.format_number_string(d.get("total_employees", "N/A"))}</b>', d_empty["total_employees_source_complete"], False),
            ('<b>Advisory employees</b>', f'<b>{self.format_number_string(d.get("advisory_employees", "N/A"))}</b>', d_empty["advisory_employees_source_complete"], True),
            ('<b>Locations (#)</b>', f'<b>{n_locations}</b>', locations_source, False),
            ('<b>International locations</b>', f'<b>{self.format_number_string(international_locations)}</b>', d_empty["international_locations_source_complete"], True),
            ('<b>Client focus:</b>', d["client_focus"], d_empty["client_focus_source_complete"], False),
            ('<b>Services:</b>', '; '.join(d["product_summary_noverb"].split(', ')), d_empty["product_summary_source_complete"], False),
            (None, '\u00A0&nbsp;\u00A0&nbsp;'.join(key for key in service_column_values), empty_str, False),
            checkbox_values_result,
            ('<b>Ownership type:</b>', d["ownership_type"], d_empty["ownership_type_source_complete"], False),
            ('<b>Ownership detail:</b>', '; '.join(str(d["ownership_detail"]).split('; ')), d_empty["ownership_detail_source_complete"], False),
            ('<b>Year founded:</b>', self.format_number_string(d.get("firm_years_summary", "N/A")).replace(",", ""), d_empty["firm_years_summary_source_complete"], False),
            ('<b>Website:</b>', self.make_link(website_url, data['cib_id']), empty_str, True),
        ]

        result = pd.DataFrame(data=result_list, columns=columns)
        return result
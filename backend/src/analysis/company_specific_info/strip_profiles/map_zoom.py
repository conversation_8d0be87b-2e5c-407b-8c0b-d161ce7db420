from dataclasses import dataclass
from typing import Any

import numpy as np
import pandas as pd
from src.utils.paths.csv_paths import zoom_filepaths


# CONFIG: Define separate zoom plans for lat_span and lng_span
LAT_THRESHOLDS: list[int] = [2, 5, 10, 20, 50, 100, 250]
LAT_ZOOMS: list[float] = [5, 4.5, 3.8, 2.2, 1.4, 1, 0]

LNG_THRESHOLDS: list[int] = [2, 5, 10, 20, 50, 100, 250]
LNG_ZOOMS: list[float] = [5, 4.2, 3.8, 3, 2.3, 1.5, 0]

SPECIAL_ZOOMS: dict[str, float] = {
    'United States of America': 2.5, 'Germany': 4.1, 'Japan': 3.55,
    'India': 2.80, 'United Kingdom': 3.65, 'Italy': 3.85,
    'Brazil': 2.4, 'Canada': 1.6, 'Mexico': 3.2,
    'Australia': 2.55, 'Spain': 3.3, 'Indonesia': 2.7,
    'Netherlands': 5.5, 'Turkey': 4, 'Saudi Arabia': 3.5,
    'Switzerland': 4.5, 'Poland': 4.4
}


boundaries_df = pd.read_csv(zoom_filepaths.boundaries)
missing_countries_df = pd.read_csv(zoom_filepaths.missing_countries)

missing_country_map: dict[str, str] = dict(zip(
    missing_countries_df.iloc[:, 0],
    missing_countries_df.iloc[:, 1]
))


@dataclass(kw_only=True, slots=True)
class Coordinate:
    north: np.float64
    south: np.float64
    east: np.float64
    west: np.float64


def compute_single_zoom(coordinate: Coordinate, countries: list[str]) -> float:
    if len(countries) == 1:
        c = countries[0]

        if c in SPECIAL_ZOOMS:
            return SPECIAL_ZOOMS[c]

    lat_span = abs(coordinate.north - coordinate.south)
    lng_span = abs(coordinate.east - coordinate.west) / 1.15

    if lat_span >= lng_span:
        span = lat_span
        thresholds = LAT_THRESHOLDS
        zooms = LAT_ZOOMS
    else:
        span = lng_span
        thresholds = LNG_THRESHOLDS
        zooms = LNG_ZOOMS

    if span < thresholds[0]:
        return zooms[0]
    if span > thresholds[-1]:
        return zooms[-1]

    for i in range(len(thresholds) - 1):
        low, high = thresholds[i], thresholds[i + 1]
        if low <= span <= high:
            z_low, z_high = zooms[i], zooms[i + 1]
            zoom_raw = z_low + ((span - low) / (high - low)) * (z_high - z_low)
            return float(np.floor(zoom_raw * 10) / 10.0)
    return zooms[-1]


def normalize_countries(a: list[str]) -> list[str]:
    if not a:
        return []
    normalized_countries = []
    
    for _country in a:
        if not _country:
            continue
        _normalized_country = missing_country_map.get(_country, _country)
        if _normalized_country and _normalized_country.upper() != '#NA':
            normalized_countries.append(_normalized_country)
    return normalized_countries


def compute_zoom(cib_id: str, company_name: str, countries: list[str]) -> dict[str, Any]:
    normalized_countries: list[str] = normalize_countries(countries)
    
    if not normalized_countries:
        return {}

    company_bounds = boundaries_df[boundaries_df['country'].isin(normalized_countries)]
    if company_bounds.empty:
        return {}

    north: np.float64 = company_bounds['bounds_north'].max()
    south: np.float64 = company_bounds['bounds_south'].min()
    east: np.float64 = company_bounds['bounds_east'].max()
    west: np.float64 = company_bounds['bounds_west'].min()
    coordiante = Coordinate(north=north, south=south, east=east, west=west)

    mid_lat: np.float64 = (north + south) / 2
    mid_lng: np.float64 = (east + west) / 2
    zoom: float = compute_single_zoom(coordinate=coordiante, countries=normalized_countries)

    return {
        'cib_id':       cib_id,
        'company_name': company_name,
        'north':        north,
        'south':        south,
        'east':         east,
        'west':         west,
        'mid_lat':      mid_lat,
        'mid_lng':      mid_lng,
        'zoom':         zoom
    }

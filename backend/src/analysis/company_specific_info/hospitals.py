import numpy as np
import pandas as pd
import src.utils.paths.csv_paths as csv_paths

pd.options.mode.chained_assignment = None

NA_CATEGORY = "NA"

df = pd.read_csv(
    csv_paths.HOSPITALS, 
    dtype={"cib_id": "category", "kis_provider": "category"},
    na_values=[NA_CATEGORY],
    na_filter=False,
)

#print(df)

def get_by_acquiror_target(acquiror_url: str, target_url: str) -> dict[str, list]:
    if acquiror_url == "" or target_url == "":
        raise ValueError("Acquiror and target URLs must not be empty.")
    
    if acquiror_url == NA_CATEGORY or target_url == NA_CATEGORY:
        raise ValueError(f"Acquiror and target URLs must not be '{NA_CATEGORY}'.")
    
    acquiror_group = df[df["cib_id"] == acquiror_url]
    target_group = df[df["cib_id"] == target_url]

    if acquiror_group.empty:
        raise ValueError(f"Acquiror company URL '{acquiror_url}' not found.")

    if target_group.empty:
        raise ValueError(f"Target company URL '{target_url}' not found.")

    common_his: list = np.intersect1d(acquiror_group["kis_provider"], target_group["kis_provider"]).tolist()
    acquiror_only_his: list = np.setdiff1d(acquiror_group["kis_provider"], target_group["kis_provider"]).tolist()
    target_only_his: list = np.setdiff1d(target_group["kis_provider"], acquiror_group["kis_provider"]).tolist()

    return {
        "common_his": sorted(common_his, key=str.lower), 
        "acquiror_only_his": sorted(acquiror_only_his, key=str.lower), 
        "target_only_his": sorted(target_only_his, key=str.lower), 
    }

import pandas as pd
import src.utils.paths.csv_paths as csv_paths


wealh_management_companies = pd.read_csv(csv_paths.PATH_WEALTH_MANAGEMENT_DATA, sep=";")

wealh_management_companies['AUM ($bn)'] = pd.to_numeric(wealh_management_companies['AUM ($bn)'].str.replace(',', '.').str.replace(' ', ''))
wealh_management_companies['No. of clients'] = pd.to_numeric(wealh_management_companies['No. of clients'].str.replace(',', '.').str.replace(' ', ''))
wealh_management_companies['Average AUM / client'] = pd.to_numeric(wealh_management_companies['Average AUM / client'].str.replace(',', '.').str.replace(' ', ''))

def get_data(cib_ids):
    df = wealh_management_companies

    filtered_df = df[df['cib_id'].isin(cib_ids)]

    return filtered_df


#print(wealh_management_companies)
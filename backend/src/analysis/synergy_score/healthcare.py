import src.analysis.company_specific_info.hospitals as hospitals
import src.analysis.company_specific_info.hospitals_specializations as hospitals_specializations
import math


def get_label(actual_value, threshold_low, threshold_high):
    if actual_value < threshold_low:
        return "low"
    elif actual_value > threshold_high:
        return "high"
    else:
        return "medium"


def determine_his_text_and_label(common_his, acquiror_only_his, target_only_his):
    if common_his:
        text = f"Both firms run on {', '.join(common_his)} platform(s)"
        label = "high"
    elif acquiror_only_his and target_only_his:
        text = (f"Different hospital information system providers - "
                f"{', '.join(target_only_his)} for target vs. {', '.join(acquiror_only_his)} for buyer")
        label = "low"
    elif target_only_his and acquiror_only_his:
        text = (f"Some hospital information platform overlap - target runs on "
                f"{', '.join(target_only_his)}; buyer has some {', '.join(acquiror_only_his)} exposure")
        label = "medium"
    else:
        text = "No clear hospital information system pattern identified"
        label = "low"

    return text, label

def get_hospital_synergies(buyer_cib_id, target_cib_id):
    # hospital stuff
    data = hospitals.get_by_acquiror_target(buyer_cib_id, target_cib_id)

    common_his = data["common_his"]
    acquiror_only_his = data["acquiror_only_his"]
    target_only_his = data["target_only_his"]

    text, label = determine_his_text_and_label(common_his, acquiror_only_his, target_only_his)

    score = {'title': 'IT Infrastructure',
             'text':  text,
             'label': label,
             "shaded": False}

    data2 = hospitals_specializations.get_by_acquiror_target(buyer_cib_id, target_cib_id)

    bed_capacity_overlap = data2["bed_capacity_overlap"]
    bed_capacity_label = get_label(bed_capacity_overlap, 0.3, 0.5)

    score2 = {'title': 'Procurement - medical devices and consumables',
              'text': f'{math.ceil(bed_capacity_overlap * 100)}%  of target bed capacity overlaps with buyer specializations',
              'label': bed_capacity_label,
              "shaded": False}


    return [score, score2]
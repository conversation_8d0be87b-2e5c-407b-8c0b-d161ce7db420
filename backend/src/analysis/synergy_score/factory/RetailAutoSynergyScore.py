import src.analysis.synergy_score.factory.SynergyScore as base1
import src.data_platform.DB_connector as DB
import math
import src.analysis.company_specific_info.retail_cars as retail_cars

base = base1.SynergyScore



class RetailAutoSynergyScore(base):

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        procurement = RetailAutoSynergyScore.procurement(buyer_cib_id, target_cib_id)
        footprints = base.get_footprints(buyer_cib_id, target_cib_id, self.sector_id, self.engine, "Footprint consolidation")
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id, self.engine)
        location_overlap = footprints.get('value', 0.0)
        cross_selling_countries = base.get_crossselling_potential_local(buyer_cib_id, target_cib_id, location_overlap, self.engine)
        incomes = base.get_average_income(buyer_cib_id, target_cib_id, self.engine)

        return base.remove_empty_dicts([procurement, footprints, overhead_rationalization, cross_selling_countries, incomes])

    @staticmethod
    def procurement(buyer_cib_id, target_cib_id):
        try:
            data2 = retail_cars.get_by_acquiror_target(buyer_cib_id, target_cib_id)

            common_brands = len(data2["brands"]["common"])
            all_target_brands = len(data2["brands"]["target_only"]) + common_brands
            overlap = common_brands / all_target_brands

            overlap_label = base.get_label(overlap, 0.3, 0.5)

            result = {'title': 'Procurement - common brands',
                      'text': f'{math.ceil(overlap * 100)}%  of target brands overlap with buyer brands ({common_brands} common out of {all_target_brands} target brands).',
                      'label': overlap_label,
                      "shaded": False}
        except Exception as e:
            result = {}  #{"Bed capacity - error": str(e)}

        return result




#engine = DB.engine
#x = RetailAutoSynergyScore(engine, 18).get_synergy_score('sonicautomotive.com', 'asburyauto.com')
#print(x)



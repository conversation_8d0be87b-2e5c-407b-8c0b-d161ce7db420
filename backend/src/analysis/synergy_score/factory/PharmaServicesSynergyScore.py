import src.analysis.synergy_score.factory.SynergyScore as base1
import src.data_platform.DB_connector as DB
import math
import src.analysis.company_specific_info.hospitals_specializations as hospitals_specializations
import src.analysis.company_specific_info.hospitals as hospitals

base = base1.SynergyScore

# class PharmaServicesSynergyScore(base):
#
#     def get_synergy_score(self, buyer_cib_id, target_cib_id, sector_id):
#         footprints = base.get_footprints(buyer_cib_id, target_cib_id, sector_id, self.engine)
#         overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id)
#         cross_selling_local = base.get_crossselling_potential_local(buyer_cib_id, target_cib_id, footprints['value'], self.engine)
#         cross_selling_countries = base.get_crossselling_potential_countries(buyer_cib_id, target_cib_id)
#         cost_development = base.cost_development(buyer_cib_id, target_cib_id)
#         cost1 = cost_development[0]
#         cost2 = cost_development[1]
#
#         return [footprints, overhead_rationalization, cross_selling_local, cross_selling_countries, cost1, cost2]



class PharmaceuticalSynergyScore(base):

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)
        cross_selling_countries = base.get_crossselling_potential_countries(buyer_cib_id, target_cib_id, engine_or_conn=self.engine) # consumer reach increase based on sales locations only for both firms
        
        if 'title' in cross_selling_countries:
            cross_selling_countries['shaded'] = True
        
        cost_development = base.cost_development(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)
        
        if cost_development:
            for a in cost_development:
                if a['title'] in {'R&D location cost development', 'Manufacturing location cost development'}:
                    a['shaded'] = True
        
        country_risk = base.get_country_risk(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)

        # why sometimes we get no cost estimates?!

        data = [overhead_rationalization, *cost_development, cross_selling_countries, country_risk]
        return base.remove_empty_dicts(data)

class PharmaServicesSynergyScore(base):

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        footprints = base.get_footprints(buyer_cib_id, target_cib_id, self.sector_id, engine_or_conn=self.engine)

        if 'title' in footprints:
            footprints['title'] = 'Footprint consolidation'
        
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)
        cross_selling_countries = base.get_crossselling_potential_countries(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)

        if 'title' in cross_selling_countries:
            cross_selling_countries['title'] = 'Cross-selling potential'

        return base.remove_empty_dicts([footprints, overhead_rationalization, cross_selling_countries])


class HealthcareProviders(base):

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        footprints = base.get_footprints(buyer_cib_id, target_cib_id, self.sector_id, self.engine)
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id)
        cross_selling_local = base.get_crossselling_potential_local(buyer_cib_id, target_cib_id, footprints['value'], self.engine)
        income = base.get_average_income(buyer_cib_id, target_cib_id)
        return base.remove_empty_dicts([footprints, overhead_rationalization, cross_selling_local, income])

class ClinicsBase(base):

    def __init__(self, engine, sector_id, footprint_title='Footprint consolidation'):
        self.engine = engine
        self.sector_id = sector_id
        self.footprint_title = footprint_title

    @staticmethod
    def bed_capacity_overlap(buyer_cib_id, target_cib_id):
        try:
            data2 = hospitals_specializations.get_by_acquiror_target(buyer_cib_id, target_cib_id)

            bed_capacity_overlap = data2["bed_capacity_overlap"]
            bed_capacity_label = base.get_label(bed_capacity_overlap, 0.3, 0.5)

            result = {'title': 'Procurement - medical devices and consumables',
                      'text': f'{math.ceil(bed_capacity_overlap * 100)}%  of target bed capacity overlaps with buyer specializations',
                      'label': bed_capacity_label,
                      "shaded": False}
        except Exception as e:
            result = {}  #{"Bed capacity - error": str(e)}

        return result

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        procurement = ClinicsBase.bed_capacity_overlap(buyer_cib_id, target_cib_id)
        footprints = base.get_footprints(buyer_cib_id, target_cib_id, self.sector_id, self.engine, self.footprint_title)
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id, self.engine)
        location_overlap = footprints.get('value', 0.0)
        cross_selling_countries = base.get_crossselling_potential_local(buyer_cib_id, target_cib_id, location_overlap, self.engine)
        incomes = base.get_average_income(buyer_cib_id, target_cib_id, self.engine)
        return base.remove_empty_dicts([procurement, footprints, overhead_rationalization, cross_selling_countries, incomes])


class ClinicsWithIT(ClinicsBase):
    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        it_infrastructure = ClinicsWithIT.it_infrastructure(buyer_cib_id, target_cib_id)
        base_data = super().get_synergy_score(buyer_cib_id, target_cib_id)
        
        if it_infrastructure:
            base_data.insert(2, it_infrastructure)
        return base.remove_empty_dicts(base_data)

    @staticmethod
    def determine_his_text_and_label(common_his, acquiror_only_his, target_only_his):
        if common_his:
            text = f"Both firms run on {', '.join(common_his)} platform(s)"
            label = "high"
        elif acquiror_only_his and target_only_his:
            text = (f"Different hospital information system providers - "
                    f"{', '.join(target_only_his)} for target vs. {', '.join(acquiror_only_his)} for buyer")
            label = "low"
        elif target_only_his and acquiror_only_his:
            text = (f"Some hospital information platform overlap - target runs on "
                    f"{', '.join(target_only_his)}; buyer has some {', '.join(acquiror_only_his)} exposure")
            label = "medium"
        else:
            text = "No clear hospital information system pattern identified"
            label = "low"

        return text, label

    @staticmethod
    def it_infrastructure(buyer_cib_id, target_cib_id):
        try:
            data = hospitals.get_by_acquiror_target(buyer_cib_id, target_cib_id)

            common_his = data["common_his"]
            acquiror_only_his = data["acquiror_only_his"]
            target_only_his = data["target_only_his"]

            text, label = ClinicsWithIT.determine_his_text_and_label(common_his, acquiror_only_his, target_only_his)

            score = {'title': 'IT Infrastructure',
                     'text': text,
                     'label': label,
                     "shaded": False}
        except Exception as _:
            score = {}  #{"IT Infrastrcture - error": str(e)}

        return score

engine = DB.engine
#x = PharmaServicesSynergyScore(engine).get_synergy_score('vestas.com', 'nordex-online.com', 18)
#x = HealthcareProviders(engine).get_synergy_score('vestas.com', 'nordex-online.com', 18)

#x = ClinicsWithIT(engine).get_synergy_score('mediclin.de', 'schoen-klinik.de', 18)

#data = hospitals.get_by_acquiror_target('mediclin.de', 'schoen-klinik.de')

#print(x)



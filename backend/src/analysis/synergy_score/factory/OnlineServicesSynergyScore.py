import src.analysis.synergy_score.factory.SynergyScore as base1
import src.data_platform.DB_connector as DB
import math
import src.analysis.company_specific_info.ecommerce_brands as ecommerce_brands
base = base1.SynergyScore



class OnlineServicesSynergyScore(base):

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        procurement = self.procurement(buyer_cib_id, target_cib_id)
        overhead_rationalization = base.get_overhead_rationalization(buyer_cib_id, target_cib_id, engine_or_conn=self.engine, title='Overhead, GTM, and tech rationalization')
        cross_selling_countries = base.get_crossselling_potential_countries(buyer_cib_id, target_cib_id, engine_or_conn=self.engine) # consumer reach increase based on sales locations only for both firms
        cost_development = base.cost_development(buyer_cib_id, target_cib_id, engine_or_conn=self.engine)

        # why sometimes we get no cost estimates?!

        data = [procurement, overhead_rationalization, *cost_development, cross_selling_countries]
        return base.remove_empty_dicts(data)

    @staticmethod
    def procurement(buyer_cib_id, target_cib_id):
        try:
            data2 = ecommerce_brands.get_by_acquiror_target(buyer_cib_id, target_cib_id)

            common_brands = int(data2["brands"]["common_no"])
            all_target_brands = int(data2["brands"]["target_no"])
            overlap = common_brands / all_target_brands

            overlap_label = base.get_label(overlap, 0.3, 0.5)

            result = {'title': 'Procurement - common brands',
                      'text': f'{math.ceil(overlap * 100)}%  of target brands overlap with buyer brands ({common_brands} common out of {all_target_brands} target brands).',
                      'label': overlap_label,
                      "shaded": False}
        except Exception as e:
            result = {}  # {"Bed capacity - error": str(e)}

        return result




#engine = DB.engine
#x = RetailAutoSynergyScore(engine, 27).get_synergy_score('corporate.zalando.com', 'aboutyou.de')
#print(x)



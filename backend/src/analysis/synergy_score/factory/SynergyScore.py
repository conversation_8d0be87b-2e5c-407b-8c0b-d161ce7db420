import pandas as pd
import src.data_platform.DB_connector as DB
import math
import src.analysis.overlaps as overlaps
import src.analysis.company_extended_metrics.population as population
import src.analysis.country_risk as country_risk
import src.analysis.country_salaries as country_income
import src.analysis.company_extended_metrics.income as income


pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)


class SynergyScore():

    def __init__(self, engine, sector_id):
        self.engine = engine
        self.sector_id = sector_id

    def get_synergy_score(self, buyer_cib_id, target_cib_id):
        pass


    @staticmethod
    def remove_empty_dicts(lst):
        return [item for item in lst if not (isinstance(item, dict) and not item)]


    @staticmethod
    def get_label(actual_value, threshold_low, threshold_high):
        if actual_value < threshold_low:
            return "low"
        elif actual_value > threshold_high:
            return "high"
        else:
            return "medium"

    @staticmethod
    def get_footprints(buyer_cib_id, target_cib_id, sector_id, engine_or_conn, title='Procurement - local services'):
        sectors = pd.read_sql_query(
            f"SELECT 'overlap_' || s.sector_radius_km as default_overlap_2 FROM sectors s where s.sector_id={sector_id}", engine_or_conn)
        sector_overlap = sectors['default_overlap_2'].iloc[0].split('_')[1]

        location_overlap_data = DB.get_overlaps(buyer_cib_id, target_cib_id, function_id=0, country_id=0, engine_or_conn=engine_or_conn)
        location_overlap_value = float(location_overlap_data.query('country_id == 0')[f'overlap_{sector_overlap}'].iloc[0])

        if location_overlap_value == 0.0:
            footprint_consolidation = "Target locations are not within buyer's reach"
        else:
            footprint_consolidation = f"{int((math.ceil(location_overlap_value * 100) / 100) * 100)}% of target locations are within {sector_overlap}km of buyer locations"

        footprint_dict = {
            "title": title,
            "text": footprint_consolidation,
            "value": location_overlap_value,
            "label": SynergyScore.get_label(location_overlap_value, 0.1, 0.3),
            "shaded": False
        }

        return footprint_dict

    @staticmethod
    def get_overhead_rationalization(buyer_cib_id, target_cib_id, engine_or_conn=None, title = "Overhead rationalization"):
        country_overlap_data = overlaps.get_country_intersection(buyer_cib_id, [target_cib_id], engine_or_conn)

        country_overlap_numbers = len(country_overlap_data['common_countries'].iloc[0]) / len(
            country_overlap_data['target_countries'].iloc[0])

        if country_overlap_numbers >= 0.99:
            text_for_fe = "Both buyer's and target's operations are in the same market(s)"
            label = "high"
        else:
            text_for_fe = f"{int(country_overlap_numbers * 100)}% of target country organizations overlap"
            label = SynergyScore.get_label(country_overlap_numbers, 0.1, 0.3)

        country_overlap_dict = {
            "title": title,
            "text": text_for_fe,
            "label": label,
            "shaded": False
        }

        return country_overlap_dict

    @staticmethod
    def get_crossselling_potential_local(buyer_cib_id, target_cib_id, location_overlap_value, engine):
        all_locations_target_value = pd.read_sql_query(f"SELECT COUNT(*) FROM locations where cib_id='{target_cib_id}'", engine)['count'].iloc[0]
        all_locations_buyer_value = pd.read_sql_query(f"SELECT COUNT(*) FROM locations where cib_id='{buyer_cib_id}'", engine)['count'].iloc[0]

        new_markets_number = (1-location_overlap_value) * all_locations_target_value
        new_markets_perc = new_markets_number / all_locations_buyer_value

        population_increase_data = population.get_population_increase_locations(buyer_cib_id, [target_cib_id])

        if population_increase_data.empty:
            population_increase_value = 0.0
        else:
            population_increase_value = float(population_increase_data['absolute_population_increase'].iloc[0])

        cross_selling_dict = {
            "title": "Other - new market entry",
            "value_extra_population": population_increase_value,
            "value_extra_markets": new_markets_number,
            "text": f"Adds {new_markets_number:.0f} additional local markets with {math.ceil(population_increase_value):.0f}m new potential customers",
            "label": SynergyScore.get_label(new_markets_perc, 0.7, 0.9),
            "shaded": True
        }
        return cross_selling_dict


    @staticmethod
    def get_crossselling_potential_countries(buyer_cib_id, target_cib_id, engine_or_conn=None):
        population_increase_data = population.get_population_increase_countries(buyer_cib_id, [target_cib_id], engine_or_conn=engine_or_conn)

        if population_increase_data.empty or population_increase_data['population_increase'][0] == 0:
            return {}
        
        population_increase_value = float(population_increase_data['population_increase'].iloc[0])
        new_countries = len(population_increase_data['countries_increase'].iloc[0])
        perc_increase = population_increase_data['percentage_increase'].iloc[0]

        cross_selling_dict = {
            "title": "Other - new market entry",
            "text": f"Adds {new_countries} additional countries with {math.ceil(population_increase_value):.0f}m new potential customers",
            "label": SynergyScore.get_label(perc_increase, 0.7, 0.9),
            "shaded": False
        }
        return cross_selling_dict


    @staticmethod
    def cost_development(buyer_cib_id, target_cib_id, engine_or_conn=None):
        r_and_d_data = country_income.get_country_salaries(buyer_cib_id, target_cib_id, function_id=12, engine_or_conn=engine_or_conn)
        manufacturing_data = country_income.get_country_salaries(buyer_cib_id, target_cib_id, function_id=6, engine_or_conn=engine_or_conn)

        if r_and_d_data.empty:
            r_and_d_result = {}
        else:
            r_and_d_result = {
                "title": "R&D location cost development",
                "text": f"Change in Ø R&D labor cost per FTE {r_and_d_data['pct_change'].iloc[0]:.1%}",
                "label": SynergyScore.get_label(r_and_d_data['pct_change'].iloc[0], 0.7, 0.9),
                "shaded": False
            }

        if manufacturing_data.empty:
            manufacturing_result = {}
        else:
            manufacturing_result = {
                "title": "Manufacturing location cost development",
                "text": f"Change in Ø manufacturing labor cost per FTE {manufacturing_data['pct_change'].iloc[0]:.1%}",
                "label": SynergyScore.get_label(manufacturing_data['pct_change'].iloc[0], 0.7, 0.9),
                "shaded": False
            }

        return r_and_d_result, manufacturing_result


    @staticmethod
    def get_country_risk(buyer_cib_id, target_cib_id, engine_or_conn=None):
        risk_data = country_risk.get_country_risk(buyer_cib_id, target_cib_id, engine_or_conn=engine_or_conn)

        if (risk_data.empty
            or risk_data.isna().sum().any() 
        ):
            return {}

        delta_risk = risk_data['combined_risk'].iloc[0] - risk_data['buyer_risk'].iloc[0]

        risk_result = {
            "title": "Country risk",
            "text": f"Buyer risk: {risk_data['buyer_risk'].iloc[0]:.1f}, Target risk: {risk_data['target_risk'].iloc[0]:.1f}, Combined risk: {risk_data['combined_risk'].iloc[0]:.1f}",
            "label": SynergyScore.get_label(risk_data['combined_risk'].iloc[0], 0.7, 0.9),
            "shaded": True
        }

        return risk_result

    @staticmethod
    def get_average_income(buyer_cib_id, target_cib_id, engine=None):
        extra_income_data = income.get_income_increase_locations_synergy_score(buyer_cib_id, [target_cib_id], engine_or_conn=engine)

        if extra_income_data.empty:
            return {}

        extra_income_value = float(extra_income_data['average_income_incremental'].iloc[0])

        print(income.get_average_income_company(buyer_cib_id))

        buyer_income_value = income.get_average_income_company(buyer_cib_id)['average_income'].iloc[0]

        label_income = SynergyScore.get_label(extra_income_value / buyer_income_value, 0.95, 1.05)

        if label_income == "low":
            word = "below"
        elif label_income == "high":
            word = "above"
        else:
            word = "in line with"

        text = f"Newly added consumer reach reflects ${extra_income_value}k per capita income - {word} ${math.ceil(buyer_income_value):.1f}k per capita income for current buyer markets [analysis currently limited to Western markets]"

        income_dict = {
            "title": "Other - new market attractiveness",
            "extra_income_value": extra_income_value,
            "buyer_income_value": buyer_income_value,
            "text": text,
            "label": label_income,
            "shaded": True
        }

        return income_dict
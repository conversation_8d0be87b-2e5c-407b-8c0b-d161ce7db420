from src.analysis.synergy_score.factory.WealthManagementSynergyScore import WMSynergyScore
import src.analysis.synergy_score.factory.PharmaServicesSynergyScore as s
import src.analysis.synergy_score.factory.RetailAutoSynergyScore as auto
import src.analysis.synergy_score.factory.OnlineServicesSynergyScore as online

from src.analysis.synergy_score.factory.SynergyScore import SynergyScore
import src.data_platform.DB_connector as DB

engine = DB.engine


def sector_id_2_synergy_score(conn, sector_ids) -> SynergyScore:
    mappings = {
        # sector 31 'Pharmaceuticals'
        31: s.PharmaceuticalSynergyScore(conn, 31),
        
        # sector 41 'Wealth Management / Brokerage'
        41: WMSynergyScore(conn, 41),

        # sector 18 'Healthcare Providers'
        123: s.ClinicsWithIT(conn, 123),
        124: s.ClinicsWithIT(conn, 124),
        126: s.HealthcareProviders(conn, 126),
        127: s.ClinicsWithIT(conn, 127),
        129: s.ClinicsBase(conn, 129),
        147: s.HealthcareProviders(conn, 147),
        161: s.ClinicsWithIT(conn, 161),

        164: s.ClinicsWithIT(conn, 164),

        # sector 157 'Pharma Services'
        158: s.PharmaServicesSynergyScore(conn, 158),
        159: s.PharmaServicesSynergyScore(conn, 159),
        160: s.PharmaceuticalSynergyScore(conn, 160),

        # like veterinary care
        4: s.ClinicsBase(conn, 4, footprint_title='Footprint consolidation'),
        20: s.ClinicsBase(conn, 20, footprint_title='Footprint consolidation'),
        34: s.ClinicsBase(conn, 34, footprint_title='Footprint consolidation'),
        37: s.ClinicsBase(conn, 37, footprint_title='Footprint consolidation'),
        57: s.ClinicsBase(conn, 57, footprint_title='Footprint consolidation'),
        68: s.ClinicsBase(conn, 68, footprint_title='Footprint consolidation'),
        78: s.ClinicsBase(conn, 78, footprint_title='Footprint consolidation'),

        77: auto.RetailAutoSynergyScore(conn, 77),
        27: online.OnlineServicesSynergyScore(conn, 27),
        17: s.PharmaceuticalSynergyScore(conn, 17)
    }

    for sector_id in sector_ids:
        if sector_id in mappings.keys():
            print("found: ", sector_id)
            return mappings[sector_id]

    raise ValueError(f"No Synergy Score available for sector IDs {sector_ids}")

import src.analysis.company_specific_info.wealth_management as wealth_management
import src.analysis.company_specific_info.banks as banks


def get_wealth_band_label(ratio):
    if 0.75 <= ratio <= 1.25:
        return "high"
    elif 0.5 <= ratio <= 1.5:
        return "medium"
    else:
        return "low"



def determine_his_text_and_label(common_his, acquiror_only_his, target_only_his):
    if common_his:
        text = f"Both firms run on {', '.join(common_his)} platform(s)"
        label = "high"
    elif acquiror_only_his and target_only_his:
        text = (f"Different core banking system providers - "
                f"{', '.join(target_only_his)} for target vs. {', '.join(acquiror_only_his)} for buyer")
        label = "low"
    elif target_only_his and acquiror_only_his:
        text = (f"Some core banking system overlap - target runs on "
                f"{', '.join(target_only_his)}; buyer has some {', '.join(acquiror_only_his)} exposure")
        label = "medium"
    else:
        text = "No clear core banking system pattern identified"
        label = "low"

    return text, label

def get_wm_synergies(buyer_cib_id, target_cib_id):
    try:
        data = banks.get_by_acquiror_target(buyer_cib_id, target_cib_id)

        common_his = data["common_cbs"]
        acquiror_only_his = data["acquiror_only_cbs"]
        target_only_his = data["target_only_cbs"]

        text, label = determine_his_text_and_label(common_his, acquiror_only_his, target_only_his)

        score = {'title': 'IT Infrastructure',
                 'text':  text,
                 'label': label,
                 "shaded": False}
    except Exception as e:
        score = {} #{"IT Infrastrcture - error": str(e)}

    try:
        buyer_average_aum = wealth_management.get_data([buyer_cib_id])['Average AUM / client'].iloc[0]
        target_average_aum = wealth_management.get_data([target_cib_id])['Average AUM / client'].iloc[0]


        if (target_average_aum  < 0.5 and buyer_average_aum < 0.5) or (target_average_aum > 20 and buyer_average_aum > 20):
            text = f"Target client average AUM of ${target_average_aum}m per client is in-line with buyer's current clients' average AUM of ${buyer_average_aum}m per client - may allow for full harmonization of coverage models."
            aum_overlap_label = "high"
        else:
            aum_overlap_label = get_wealth_band_label(target_average_aum / buyer_average_aum)
            if aum_overlap_label == "low":
                text = f"Target client average AUM of ${target_average_aum}m per client is meaningully different from buyer's current clients' average AUM of ${buyer_average_aum}m per client - implies need to maintain separate separate coverage models."
            elif aum_overlap_label == "high":
                text = f"Target client average AUM of ${target_average_aum}m per client is in-line with buyer's current clients' average AUM of ${buyer_average_aum}m per client - may allow for full harmonization of coverage models."
            else:
                text = f"Target client average AUM of ${target_average_aum}m per client is different from buyer's current clients' average AUM of ${buyer_average_aum}m per client - may limit opportunity to harmonize coverage model."

        score2 = {
            "title": "Client wealth bands",
            "buyer_average_aum": buyer_average_aum,
            "target_average_aum": target_average_aum,
            "text": text,
            "label": aum_overlap_label,
            "shaded": False
        }
    except Exception as e:
        score2 = {}  #{"Wealth bands - error": str(e)}

    return [s for s in [score, score2] if s != {}]

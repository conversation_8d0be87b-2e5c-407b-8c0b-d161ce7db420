import pandas as pd
import math
import src.data_platform.DB_connector as DB
import src.analysis.overlaps as overlaps
import src.analysis.company_extended_metrics.population as population
import src.analysis.company_extended_metrics.income as income

import src.analysis.synergy_score.healthcare as sector_hospitals
import src.analysis.synergy_score.wealth_management as sector_wealth_management

pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)



SECTOR_STRATEGIES = {
    "hospitals": sector_hospitals.get_hospital_synergies,
    "wealthmanagement": sector_wealth_management.get_wm_synergies,
}



def get_label(actual_value, threshold_low, threshold_high):
    if actual_value < threshold_low:
        return "low"
    elif actual_value > threshold_high:
        return "high"
    else:
        return "medium"


def get_synergy_score(buyer_cib_id, target_cib_id, sector_id, country_id, analysis_name):
    engine = DB.engine

    # sector overlap 
    # NOTE: will need rework when new schema rolls out - a lot of back-and-forth with overlap radius
    sectors = pd.read_sql_query(f"SELECT 'overlap_' || s.sector_radius_km as default_overlap_2 FROM sectors s where s.sector_id={sector_id}", engine)
    sector_overlap = sectors['default_overlap_2'].iloc[0].split('_')[1]

    # Location overlap
    try:
        location_overlap_data = DB.get_overlaps(buyer_cib_id, target_cib_id, function_id=0, country_id=0)

        location_overlap_value = float(location_overlap_data.query('function_id == 0 & country_id == 0')[f'overlap_{sector_overlap}'].iloc[0])

        footprint_consolidation = f"{int((math.ceil(location_overlap_value * 100) / 100) * 100)}% of target locations are within {sector_overlap}km of buyer locations"

        footprint_dict = {
            "title": "Procurement - local services",
            "text": footprint_consolidation,
            "value": location_overlap_value,
            "label": get_label(location_overlap_value, 0.1, 0.3),
            "shaded": False
        }
    except:
        location_overlap_value = 0.0
        footprint_dict = {
            "title": "Procurement - local services",
            "text": "Target locations are not within buyer's reach",
            "value": location_overlap_value,
            "label": get_label(location_overlap_value, 0.1, 0.3),
            "shaded": False
        }

    #country overlap
    country_overlap_data = overlaps.get_country_intersection(buyer_cib_id, [target_cib_id])

    country_overlap_numbers = len(country_overlap_data['common_countries'].iloc[0]) / len(country_overlap_data['target_countries'].iloc[0])

    if country_overlap_numbers >= 0.99:
        text_for_fe = "Both buyer's and target's operations are in the same market(s)"
        label = "high"
    else:
        text_for_fe = f"{country_overlap_numbers:.1%} of target country organizations overlap"
        label = get_label(country_overlap_numbers, 0.1, 0.3)

    country_overlap_dict = {
        "title": "Overhead rationalization",
        "text": text_for_fe,
        "label": label,
        "shaded": False
    }

    # cross-selling potential
    all_locations_target_value = pd.read_sql_query(f"SELECT COUNT(*) FROM locations where cib_id='{target_cib_id}'", engine)['count'].iloc[0]
    all_locations_buyer_value = pd.read_sql_query(f"SELECT COUNT(*) FROM locations where cib_id='{buyer_cib_id}'", engine)['count'].iloc[0]

    new_markets_number = (1-location_overlap_value) * all_locations_target_value
    new_markets_perc = new_markets_number / all_locations_buyer_value

    population_increase_data = population.get_population_increase_locations(buyer_cib_id, [target_cib_id])

    if population_increase_data.empty:
        population_increase_value = 0.0
    else:
        population_increase_value = float(population_increase_data['absolute_population_increase'].iloc[0])

    cross_selling_dict = {
        "title": "Other - new market entry",
        "value_extra_population": population_increase_value,
        "value_extra_markets": new_markets_number,
        "text": f"Adds {new_markets_number:.0f} additional local markets with {math.ceil(population_increase_value):.1f}m new potential customers",
        "label": get_label(new_markets_perc, 0.7, 0.9),
        "shaded": True
    }


    extra_income_data = income.get_income_increase_locations(buyer_cib_id, [target_cib_id], country_id=country_id)

    if extra_income_data.empty:
        income_dict = {}
    else:
        extra_income_value = float(extra_income_data['average_income_incremental'].iloc[0])


        buyer_income_value = income.get_average_income_company(buyer_cib_id)['average_income'].iloc[0]

        label_income =get_label(extra_income_value / buyer_income_value, 0.95, 1.05)

        if label_income == "low":
            word = "below"
        elif label_income == "high":
            word = "above"
        else:
            word = "in line with"

        text = f"Newly added consumer reach reflects ${extra_income_value}k per capita income - {word} ${math.ceil(buyer_income_value):.1f}k per capita income for current buyer markets [analysis currently limited to Western markets]"

        income_dict = {
            "title": "Other - new market attractiveness",
            "extra_income_value": extra_income_value,
            "buyer_income_value": buyer_income_value,
            "text": text,
            "label": label_income,
            "shaded": True
        }

    # Sector-specific part
    sector_func = SECTOR_STRATEGIES.get(analysis_name.lower())
    sector_specific_scores = sector_func(buyer_cib_id, target_cib_id) if sector_func else []


    if analysis_name == "hospitals":
        result = [
            *sector_specific_scores,
            footprint_dict,
            country_overlap_dict,
            cross_selling_dict,
            income_dict
        ]
    elif analysis_name == "wealthmanagement":

        footprint_dict["title"] = "Footprint consolidation"
        cross_selling_dict["title"] = "Cross-selling potential"
        cross_selling_dict["shaded"] = False

        result = [
            footprint_dict,
            country_overlap_dict,
            *sector_specific_scores,
            cross_selling_dict,
            income_dict,
        ]
    elif analysis_name == "insurance":

        footprint_dict["title"] = "Footprint consolidation"
        cross_selling_dict["title"] = "Cross-selling potential"

        result = [
            #footprint_dict,
            country_overlap_dict,
            cross_selling_dict,
            income_dict,
        ]

    result = [r for r in result if r != {}]

    return result
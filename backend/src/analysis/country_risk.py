
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings

engine = db_settings.ENGINE_MANAGER.get_or_create()


def get_country_risk(buyer_cib_id, target_cib_id, engine_or_conn=None):
    if engine_or_conn is None:
        engine_or_conn = engine
    
    # Parameterized query with placeholders
    query = text("""
        WITH per_cib AS (
            SELECT
                l.cib_id,
                COUNT(CASE WHEN cr.risk_grade is not null THEN 1 END) AS location_count,
                SUM(CASE WHEN cr.risk_grade is not null THEN cr.risk_grade::numeric END) AS total_risk
            FROM locations l
            JOIN countries cr 
                ON l.country_id = cr.country_id
            WHERE l.cib_id IN (:buyer_cib_id, :target_cib_id)
            GROUP BY l.cib_id
        )
        SELECT
            /* Buyer’s average risk = total risk / number of buyer locations */
            b.total_risk::numeric / b.location_count AS buyer_risk,

            /* Target’s average risk = total risk / number of target locations */
            t.total_risk::numeric / t.location_count AS target_risk,

            /* Combined average = sum of total risks / sum of all locations */
            (b.total_risk + t.total_risk)::numeric 
            / (b.location_count + t.location_count)   AS combined_risk
        FROM per_cib b
        JOIN per_cib t 
        ON b.cib_id = :buyer_cib_id
        AND t.cib_id = :target_cib_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(query, engine, params={"buyer_cib_id": buyer_cib_id, "target_cib_id": target_cib_id})
    return result

#
# x = get_country_risk('vesatas.com', 'nordex-online.com')
#
# print(x)
#
#
#
#
#
#
#
# # x = get_population_reach_countries('vestas.com', ['vestas.com', 'nordex-online'])
# # print(x)
#
# # pd.set_option('display.max_columns', None)
# # pd.set_option('display.max_rows', None)
# #
# # x = get_population_increase_locations('juliusbaer.com', ['quirinprivatbank.de'], 41)
# #
# # print(x)
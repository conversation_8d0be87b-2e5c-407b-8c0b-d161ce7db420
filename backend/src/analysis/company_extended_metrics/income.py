
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings

engine = db_settings.ENGINE_MANAGER.get_or_create()


def get_income_increase_locations(buyer_cib_id, target_cib_ids, country_id = 63, engine_or_conn=None):
    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
    WITH buyer AS (
        SELECT h3
        FROM company_footprints
        WHERE cib_id = :buyer_cib_id
    ),
    target_exclusive as (
        SELECT t.cib_id, t.h3
        FROM company_footprints t
        WHERE t.cib_id = ANY(:target_cib_ids)
        AND NOT EXISTS (SELECT 1 FROM buyer b WHERE b.h3 = t.h3)
    )
    SELECT
        te.cib_id AS target_cib_id,
        AVG(km.average_income_usd) average_income_incremental
    FROM target_exclusive te
    JOIN kontur_metrics_res7 km USING (h3)
    GROUP BY te.cib_id
    ORDER BY te.cib_id;
    """

    result = pd.read_sql(text(query), engine, params=query_params)

    if result.empty:
        return result

    result['average_income_incremental'] = round(result['average_income_incremental'] / 1000, 1)

    result['column_name'] = 'average_income_incremental'

    #country_name_query = text("SELECT country_name from countries where old_country_id = :country_id")
    #country_name = pd.read_sql(country_name_query, engine_or_conn, params={'country_id': int(country_id)}).iloc[0]['country_name']

    #result['column_label'] = f"Average gross income of incremental consumers in {country_name} ($'000)"
    result['column_label'] = f"Average gross income of incremental consumers ($'000)"

    return result


def get_income_increase_locations_synergy_score(buyer_cib_id, target_cib_ids, engine_or_conn=None):
    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
    WITH buyer AS (
        SELECT h3
        FROM company_footprints
        WHERE cib_id = :buyer_cib_id
    ),
    target_exclusive as (
        SELECT t.cib_id, t.h3
        FROM company_footprints t
        WHERE t.cib_id = ANY(:target_cib_ids)
        AND NOT EXISTS (SELECT 1 FROM buyer b WHERE b.h3 = t.h3)
    )
    SELECT
        te.cib_id AS target_cib_id,
        AVG(km.average_income_usd) AS average_income_incremental
    FROM target_exclusive te
    JOIN kontur_metrics_res7 km USING (h3)
    GROUP BY te.cib_id
    ORDER BY te.cib_id;
    """

    result = pd.read_sql(text(query), engine, params=query_params)

    if result.empty:
        return result

    result['average_income_incremental'] = round(result['average_income_incremental'] / 1000, 1)
    result['column_name'] = 'average_income_incremental'
    result['column_label'] = "Average gross income of incremental consumers ($'000)"
    return result


def get_income_locations(cib_id, country_id = 63):
    query_params = {
        'target_cib_ids': tuple(cib_id)
    }
    
    query = f"""
        SELECT
            cf.cib_id as target_cib_id,
            AVG(km.average_income_usd) AS average_income
        FROM company_footprints cf
        JOIN kontur_metrics_res7 km
        ON km.h3 = cf.h3
        where cf.cib_id IN :target_cib_ids
        GROUP BY cf.cib_id;
    """
    
    result = pd.read_sql(text(query), engine, params=query_params)

    if result.empty:
        return result

    result['average_income'] = round(result['average_income'] / 1000, 1)

    result['column_name'] = 'average_income'

    country_name = pd.read_sql(f"SELECT country_name from countries where old_country_id = {int(country_id)}", engine).iloc[0]['country_name']

    result['column_label'] = f"Average gross income of consumers in {country_name} ($'000)"

    return result



def get_average_income_company(cib_id):
    query_params = {
        'target_cib_ids': [cib_id]
    }

    query = f"""
        SELECT
            cf.cib_id as target_cib_id,
            AVG(km.average_income_usd) AS average_income
        FROM company_footprints cf
        JOIN kontur_metrics_res7 km
        ON km.h3 = cf.h3
        where cf.cib_id = ANY(:target_cib_ids)
        GROUP BY cf.cib_id;
    """

    print(query_params)

    result = pd.read_sql(text(query), engine, params=query_params)

    if result.empty:
        return result

    result['average_income'] = round(result['average_income'] / 1000, 1)

    result['column_name'] = 'average_income'

    result['column_label'] = f"Average gross income of all consumers ($'000)"

    return result



#x = get_average_income_company("juliusbaer.com")
#print(x['average_income'])



# x = get_population_reach_countries('vestas.com', ['vestas.com', 'nordex-online'])
# print(x)

# pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
#
# x = get_population_increase_locations('juliusbaer.com', ['quirinprivatbank.de'], 41)
#
# print(x)
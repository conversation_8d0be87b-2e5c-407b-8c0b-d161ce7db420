import pandas as pd
from src.config import db_settings
from sqlalchemy import Engine, text


engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()



def get_gdp_increase_country(acquiror_cib_id, cib_ids):
    query = text("""
        WITH acquiror_countries AS (
            SELECT DISTINCT l.cib_id, l.country_id
            FROM locations l
            JOIN countries cr ON cr.country_id = l.country_id
            WHERE l.cib_id = :acquiror_cib_id
        ),
        acquiror_gdp AS (
            SELECT ac.cib_id,
                ROUND(SUM(cr.gdp_bn::numeric), 3) AS acquiror_gdp
            FROM acquiror_countries ac
            JOIN countries cr ON ac.country_id = cr.country_id
            GROUP BY ac.cib_id
        ),
        target_extra_gdp AS (
            SELECT DISTINCT l.cib_id, cr.country_id, cr.gdp_bn::numeric AS gdp_bn
            FROM locations l
            JOIN countries cr ON l.country_id = cr.country_id
            LEFT JOIN acquiror_countries ac on ac.country_id = cr.country_id
            WHERE l.cib_id IN :cib_ids and ac.country_id is null
        )
        SELECT
            tc.cib_id AS target_cib_id,
            COUNT(*) AS number_countries,
            ROUND(SUM(tc.gdp_bn), 3) AS total_gdp_reach_increase,
            CASE 
                WHEN ag.acquiror_gdp = 0 THEN NULL
                ELSE ROUND(SUM(tc.gdp_bn), 3) / (ag.acquiror_gdp * 1.0)
            END AS perc_gdp_increase
        FROM target_extra_gdp tc
        JOIN acquiror_gdp ag ON true
        GROUP BY tc.cib_id, ag.acquiror_gdp;
    """)
    
    # Convert list to tuple for parameter substitution if needed by the client
    query_params = {"cib_ids": tuple(cib_ids), "acquiror_cib_id": acquiror_cib_id}
    result_df_top = pd.read_sql(query, engine, params=query_params)
    
    if result_df_top.empty:
        result_df_top = pd.DataFrame({'target_cib_id': cib_ids, 'number_countries': 0, 'total_gdp_reach_increase': 0, 'perc_gdp_increase': 0, 'indic_value': None})
        missing_cib_ids = []
    else:
        result_cib_ids = result_df_top['target_cib_id'].tolist()
        missing_cib_ids = [x for x in cib_ids if x not in result_cib_ids]
        result_df_top['indic_value'] = round(result_df_top['perc_gdp_increase'] / result_df_top['perc_gdp_increase'].max(), 3)
        #result_df_top['total_gdp_reach_increase'] = result_df_top['total_gdp_reach_increase'].apply(lambda x: "<0.1" if x == 0.0 else x)
        #result_df_top['perc_gdp_increase'] = result_df_top['perc_gdp_increase'].apply(lambda x: f"{int(x * 100)}%")


    if len(missing_cib_ids) > 0:
            missing_df = pd.DataFrame({'target_cib_id': missing_cib_ids, 'number_countries': 0, 'total_gdp_reach_increase': 0, 'perc_gdp_increase': 0, 'indic_value': None})
            result_df_top = pd.concat([result_df_top, missing_df], ignore_index=True)

    result_df_top['column_name'] = 'perc_gdp_increase'
    result_df_top['column_label'] = "Distribution reach increase based on country presence (GDP, %)"

    return result_df_top



def get_gdp_reach_country(cib_ids):
    query = text("""
        WITH target_country_gdp AS (
            SELECT DISTINCT l.cib_id, c.country_id, c.gdp_bn::numeric AS gdp_bn
            FROM locations l
            JOIN countries c ON l.country_id = c.country_id
            WHERE l.cib_id IN :cib_ids
        )
        SELECT
            tc.cib_id AS target_cib_id,
            COUNT(*) AS number_countries,
            ROUND(SUM(tc.gdp_bn), 3) AS total_gdp_reach
        FROM target_country_gdp tc
        GROUP BY tc.cib_id;
    """)

    query_params = {"cib_ids": tuple(cib_ids)}
    result_df_top = pd.read_sql(query, engine, params=query_params)

    result_df_top['indic_value'] = round(result_df_top['total_gdp_reach'] / max(result_df_top['total_gdp_reach']), 3)

    result_df_top['total_gdp_reach'] = round(result_df_top['total_gdp_reach'] / 1_000, 1)

#    result_df_top['total_gdp_reach'] = result_df_top['total_gdp_reach'].apply(lambda x: "<0.1" if x == 0.0 else x)

    result_df_top['column_name'] = 'total_gdp_reach'
    result_df_top['column_label'] = "Distribution reach based on country presence (GDP, $tn)"

    return result_df_top


#df = get_gdp_reach(['vestas.com', 'nordex-online.com'])
#print(df)

# pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
#
#
# x = get_gdp_increase('vestas.com', [ 'nordex-online.com'])
# print(x)


# def get_radius_km(sector_id):
#     engine = engine_manager.get_or_create()

#     query = text("""
#         SELECT 
#             regexp_replace(default_overlap_2, '[^0-9]', '', 'g')::int AS overlap_number
#         FROM 
#             sectors
#         WHERE 
#             sector_id = :sector_id;
#     """)

#     result = pd.read_sql(query, engine, params={"sector_id": sector_id})

#     return int(result['overlap_number'].values[0])

def get_gdp_increase_locations(buyer_cib_id, target_cib_ids, sector_id = None):
    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
    WITH buyer AS (
        SELECT h3
        FROM company_footprints
        WHERE cib_id = :buyer_cib_id
    ),
    buyer_gdp AS (
        SELECT SUM(km.gdp_usd) AS buyer_gdp
        FROM kontur_metrics_res7 km
        JOIN buyer b USING (h3)
    ),
    target_exclusive as (
        SELECT t.cib_id, t.h3
        FROM company_footprints t
        WHERE t.cib_id = ANY(:target_cib_ids)
        AND NOT EXISTS (SELECT 1 FROM buyer b WHERE b.h3 = t.h3)
    )
    SELECT
        te.cib_id AS target_cib_id,
        SUM(km.gdp_usd) / 1e9 AS absolute_gdp_increase,
        (SUM(km.gdp_usd) / 1e9) / NULLIF(bg.buyer_gdp, 0)  AS percentage_gdp_increase
    FROM target_exclusive te
    JOIN kontur_metrics_res7 km USING (h3)
    CROSS JOIN buyer_gdp bg
    GROUP BY te.cib_id, bg.buyer_gdp
    ORDER BY te.cib_id;
    """

    result = pd.read_sql(text(query), engine, params=query_params)
    
    result['indic_value'] = round(result['absolute_gdp_increase'] / max(result['absolute_gdp_increase']), 3)


    result['absolute_gdp_increase'] = round(result['absolute_gdp_increase'] / 1_000, 3)

    result['column_name'] = 'percentage_gdp_increase'
    result['column_label'] = "Distribution reach increase based on locations globally (GDP, %)"

    result = result.sort_values(by='indic_value', ascending=False)

    return result



def get_gdp_reach_locations(target_cib_ids, sector_id = None):
    query_params = {
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
        SELECT
            cf.cib_id as target_cib_id,
            SUM(km.gdp_usd) / 1000000000.0 AS absolute_gdp
        FROM company_footprints cf
        JOIN kontur_metrics_res7 km
        ON km.h3 = cf.h3
        where cf.cib_id = ANY(:target_cib_ids)
        GROUP BY cf.cib_id;
    """
    
    result = pd.read_sql(text(query), engine, params=query_params)

    result['indic_value'] = round(result['absolute_gdp'] / result['absolute_gdp'].max(skipna=True), 3)

    result['absolute_gdp'] = round(result['absolute_gdp'] / 1_000, 1)

    result['column_name'] = 'absolute_gdp'
    result['column_label'] = "Distribution reach based on locations globally (GDP, $tn)"

    result = result.sort_values(by='indic_value', ascending=False)

    return result
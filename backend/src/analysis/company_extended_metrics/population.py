import numpy as np
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings

engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()


# def get_radius_km(sector_id):
#     engine: Engine = engine_manager.get_or_create()

#     query = text("""
#         SELECT 
#             regexp_replace(default_overlap_2, '[^0-9]', '', 'g')::int AS overlap_number
#         FROM 
#             sectors
#         WHERE 
#             sector_id = :sector_id;
#     """)

#     result = pd.read_sql(query, engine, params={"sector_id": sector_id})

#     return int(result['overlap_number'].values[0])

def get_population_increase_locations(buyer_cib_id, target_cib_ids):
    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
    WITH buyer AS (
        SELECT h3
        FROM company_footprints
        WHERE cib_id = :buyer_cib_id
    ),
    buyer_pop AS (
        SELECT SUM(km.population) AS buyer_population
        FROM kontur_metrics_res7 km
        JOIN buyer b USING (h3)
    ),
    target_exclusive as (
        SELECT t.cib_id, t.h3
        FROM company_footprints t
        WHERE t.cib_id = ANY(:target_cib_ids)
        AND NOT EXISTS (SELECT 1 FROM buyer b WHERE b.h3 = t.h3)
    )
    SELECT
        te.cib_id AS target_cib_id,
        SUM(km.population) AS absolute_population_increase,
        SUM(km.population)::float / NULLIF(bp.buyer_population, 0) AS percentage_population_increase
    FROM target_exclusive te
    JOIN kontur_metrics_res7 km USING (h3)
    CROSS JOIN buyer_pop bp
    GROUP BY te.cib_id, bp.buyer_population
    ORDER BY te.cib_id;
    """

    result = pd.read_sql(text(query), engine, params=query_params)

    if result.empty:
        return pd.DataFrame()

    result['absolute_population_increase'] = round(result['absolute_population_increase'] / 1_000_000, 3)

    result['column_name'] = 'percentage_population_increase'
    result['column_label'] = "Distribution reach increase based on locations globally (consumers, %)"

    return result



def get_population_reach_countries(target_cib_ids):
    query = text("""
        WITH target_country_population AS (
            SELECT DISTINCT l.cib_id, c.old_country_id as country_id, c.population::numeric AS population
            FROM locations l
            JOIN countries c ON l.country_id = c.country_id
            WHERE l.cib_id IN :target_cib_ids
        )
        SELECT
            tp.cib_id AS target_cib_id,
            SUM(tp.population) AS absolute_population,
            array_agg(tp.country_id) as reached_countries
        FROM target_country_population tp
        GROUP BY tp.cib_id;
    """)

    query_params = {"target_cib_ids": tuple(target_cib_ids)}
    result = pd.read_sql(query, engine, params=query_params)

    result['absolute_population'] = round(result['absolute_population'] / 1_000_000, 1)

    result['column_name'] = 'absolute_population'
    result['column_label'] = "Distribution reach based on country presence (consumers, mm)"

    return result





def get_population_reach_locations(target_cib_ids):
    query_params = {
        'target_cib_ids': target_cib_ids
    }
    
    query = f"""
        SELECT
            cf.cib_id as target_cib_id,
            SUM(km.population_sum) AS absolute_population
        FROM company_footprints cf
        JOIN kontur_metrics_res7 km
        ON km.h3 = cf.h3
        where cf.cib_id = ANY(:target_cib_ids)
        GROUP BY cf.cib_id;
    """
    
    result = pd.read_sql(text(query), engine, params=query_params)

    result['column_name'] = 'absolute_population'
    result['column_label'] = "Distribution reach based on locations globally (consumers, mm)"

    return result


def get_population_increase_countries(buyer_cib_id, target_cib_ids, engine_or_conn=None):
    if engine_or_conn is None:
        engine_or_conn = engine
    
    query = text("""
        WITH acquiror_countries AS (
            SELECT DISTINCT l.cib_id, l.country_id
            FROM locations l
            JOIN countries cr ON cr.country_id = l.country_id
            WHERE l.cib_id = :buyer_cib_id
        ),
        acquiror_population AS (
            SELECT ac.cib_id,
                COALESCE(SUM(cr.population), 0) AS acquiror_population
            FROM acquiror_countries ac
            JOIN countries cr ON ac.country_id = cr.country_id
            GROUP BY ac.cib_id
        ),
        target_extra_population AS (
            SELECT DISTINCT l.cib_id, cr.old_country_id as country_id, cr.population::numeric AS population
            FROM locations l
            JOIN countries cr ON l.country_id = cr.country_id
            LEFT JOIN acquiror_countries ac on ac.country_id = cr.country_id
            WHERE l.cib_id IN :target_cib_ids and ac.country_id is null
        )
        SELECT
            tc.cib_id AS target_cib_id,
            SUM(tc.population) AS population_increase,
            array_agg(tc.country_id) AS countries_increase,
            CASE 
                WHEN ap.acquiror_population = 0 THEN NULL
                ELSE ROUND((SUM(tc.population) / ap.acquiror_population * 100), 1) 
            END AS percentage_increase
        FROM target_extra_population tc
        JOIN acquiror_population ap ON true
        GROUP BY tc.cib_id, ap.acquiror_population;
    """)
    
    # Convert list to tuple for parameter substitution if needed by the client
    query_params = {"target_cib_ids": tuple(target_cib_ids), "buyer_cib_id": buyer_cib_id}
    result = pd.read_sql(query, engine_or_conn, params=query_params)

    #add a first row into the result
    # with some valid values in case the result is empty
    if result.empty:
        result = pd.DataFrame({
            'target_cib_id': target_cib_ids,
            'population_increase': [0 * len(target_cib_ids)],
            'countries_increase': [[] * len(target_cib_ids)],
            'percentage_increase': [0 * len(target_cib_ids)]
        })


    result['indic_value'] = round(result['population_increase'] / result['population_increase'].max(), 3)

    result['population_increase'] = round(result['population_increase'] / 1_000_000, 1)

    result['column_name'] = 'population_increase'
    result['column_label'] = "Distribution reach increase based on country presence (consumers, mm)"

    return result



#x = get_population_reach_countries('vestas.com', ['vestas.com', 'nordex-online'])
# print(x)

# pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
#
# x = get_population_increase_locations('juliusbaer.com', ['quirinprivatbank.de'], 41)
#
# print(x)
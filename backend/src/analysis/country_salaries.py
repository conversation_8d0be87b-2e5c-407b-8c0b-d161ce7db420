
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings

engine = db_settings.ENGINE_MANAGER.get_or_create()


def get_country_salaries(buyer_cib_id, target_cib_id, function_id, engine_or_conn=None):
    if engine_or_conn is None:
        engine_or_conn = engine
    
    # Parameterized query with placeholders
    query = text("""
        WITH location_counts AS (
            -- Step 1: Calculate number of locations per old_country_id and company
            SELECT
                l.cib_id,
                c.old_country_id AS country_id,
                COUNT(l.location_id) AS location_count,
                SUM(c.average_income) AS total_income,
                AVG(c.average_income) AS avg_income_per_country
            FROM locations l
            JOIN location_functions lf
                ON lf.location_id = l.location_id
            JOIN countries c
                ON c.country_id = l.country_id
            WHERE l.cib_id IN (:buyer_cib_id, :target_cib_id)
            AND lf.function_id = :function_id
            GROUP BY l.cib_id, c.old_country_id
        ),
        per_cib AS (
            -- Step 2: Aggregate into JSONB per company
            SELECT
                lc.cib_id,
                SUM(lc.location_count) AS total_location_count,
                SUM(lc.total_income) AS total_income,

                -- JSON mapping: old_country_id -> avg income
                JSONB_OBJECT_AGG(lc.country_id, lc.avg_income_per_country) AS country_salary_map,

                -- JSON mapping: old_country_id -> number of locations
                JSONB_OBJECT_AGG(lc.country_id, lc.location_count) AS country_location_map
            FROM location_counts lc
            GROUP BY lc.cib_id
        )
        SELECT
            -- Buyer info
            b.cib_id AS buyer_cib_id,
            b.total_location_count AS buyer_location_count,
            b.total_income::numeric / b.total_location_count AS buyer_avg_salary,
            b.country_salary_map AS buyer_country_salaries,
            b.country_location_map AS buyer_country_locations,

            -- Target info
            t.cib_id AS target_cib_id,
            t.total_location_count AS target_location_count,
            t.total_income::numeric / t.total_location_count AS target_avg_salary,
            t.country_salary_map AS target_country_salaries,
            t.country_location_map AS target_country_locations,

            -- Combined average
            (b.total_income + t.total_income)::numeric
            / (b.total_location_count + t.total_location_count) AS combined_avg_salary,

            -- Percentage change
            (
            (
                (b.total_income + t.total_income)::numeric 
                / (b.total_location_count + t.total_location_count)
            )
            /
            (b.total_income::numeric / b.total_location_count)
            ) - 1 AS pct_change
        FROM per_cib b
        JOIN per_cib t 
        ON t.cib_id = :target_cib_id
        WHERE b.cib_id = :buyer_cib_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(query, engine, params={"buyer_cib_id": buyer_cib_id, "target_cib_id": target_cib_id, "function_id": function_id})
    return result


#x = get_country_salaries('vestas.com', 'nordex-online.com')
#print(x)
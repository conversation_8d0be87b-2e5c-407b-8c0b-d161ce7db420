from flask import Flask, jsonify, request, g, Response
from flask_restx import Api
from flask_cors import CORS
import time
from src.config import log_db_settings, settings
from src.routes.companies import register_routes_companies
from src.routes.transactions import register_routes_transactions
from src.routes.combinations import register_routes_combinations
from src.routes.routes_common import register_routes_common
from src.routes.routes_gis import register_routes_gis
from src.routes.target_search import register_routes_target_search
from src.routes.antitrust import register_routes_antitrust
from src.routes.presentation import register_routes_presentation
from src.routes.routes_system import register_routes_system
from src.routes.company_specific_info import register_routes_company_specific_info
from src.routes.whitespot import register_routes_whitespot
from src.routes.company_extended_metrics import register_routes_company_extended_metrics
from src.routes.companies_database import register_routes_companies_database

from flask import send_from_directory
import os
from src.auth import auth
import logging
import src.PostgresLogHandler as PLH

from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST


# ----- Prometheus Metrics Setup -----
REQUEST_COUNT = Counter(
    'api_requests_total',
    'Total number of requests',
    ['method', 'endpoint']
)

REQUEST_LATENCY = Histogram(
    'api_request_duration_seconds',
    'Histogram of request latency',
    ['method', 'endpoint'],
    buckets=[0.1, 0.2, 0.5, 1, 2, 3, 4, 5, 7, 10, 13, 15, 17, 20, 23, 25, 30]
)
# ------------------------------------


app = Flask(__name__, static_folder="static")

#CORS(app) #, supports_credentials=True, allow_headers=["Authorization", "Content-Type"])

CORS(app, resources={
    r"/*": {
        "origins": ["http://localhost:3000", "https://pdf-export-phi.vercel.app"],
        "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"],
        "supports_credentials": True  # Support cookies and credentials
    }
})

authorizations = {
    'Bearer Auth': {
        'type': 'apiKey',
        'in': 'header',
        'name': 'Authorization',
        'description': 'Add a Bearer token here'
    }
}


# Set up the custom Postgres logging handler
postgres_handler = PLH.PostgresLogHandler(log_db_settings.URL)
postgres_handler.setLevel(logging.INFO)

# Define a formatter for the logs (optional, since the database doesn't need formatting)
postgres_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
postgres_handler.setFormatter(postgres_formatter)

app.logger.setLevel(logging.INFO)

# Add the handler to the Flask logger
if settings.USE_DB_LOGGER:
    console_log_format = '%(asctime)s - %(name)s - %(levelname)s'
    app.logger.addHandler(postgres_handler)
    app.logger.info("Using PostgreSQL logger.")
else:
    console_log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    app.logger.info("Not using PostgreSQL logger.")

console_formatter = logging.Formatter(console_log_format)
console_handler = app.logger.handlers[0]
console_handler.setFormatter(console_formatter)


api = Api(app, version='2.0.0', title='Cloud IB Data API', description='Data Platform', security='Bearer Auth', authorizations=authorizations)


# Register routes
register_routes_companies(api, auth)
register_routes_combinations(api, auth)
register_routes_target_search(api, auth)
register_routes_common(api, auth)
register_routes_gis(api, auth)
register_routes_whitespot(api, auth)
register_routes_transactions(api, auth)
register_routes_antitrust(api, auth)
register_routes_system(api, auth)
register_routes_presentation(api, auth)
register_routes_company_specific_info(api, auth)
register_routes_company_extended_metrics(api, auth)
register_routes_companies_database(api, auth)


# ----- Prometheus Instrumentation -----

@app.before_request
def start_timer():
    """
    Called before each request.
    Sets a 'start_time' on the Flask 'g' (global context).
    """
    g.start_time = time.time()

@app.after_request
def record_metrics(response):
    """
    Called after each request.
    Calculates the duration and updates Prometheus metrics.
    """
    if hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        REQUEST_COUNT.labels(method=request.method, endpoint=request.path).inc()
        REQUEST_LATENCY.labels(method=request.method, endpoint=request.path).observe(duration)
    return response

@app.route('/metrics')
def metrics():
    """ Exposes Prometheus metrics on `/metrics`. """
    return Response(generate_latest(), content_type=CONTENT_TYPE_LATEST)


# --------------------------------------

@app.errorhandler(Exception)
def handle_exception(e):
    """Handle exceptions and log detailed information."""
    app.logger.info(f"Exception occurred: {str(e)}", exc_info=True)
    return jsonify({"error": "Internal Server Error", "message": str(e)}), 500

@app.route('/favicon.ico')
def favicon():
    return send_from_directory(os.path.join(app.root_path, 'static'),
                               'favicon.ico', mimetype='image/vnd.microsoft.icon')

if __name__ == '__main__':
    app.logger.info("Starting the Flask application...")
    # app.run(host='127.0.0.1', port=settings.PORT, debug=True)
    app.run(host='0.0.0.0', port=settings.PORT, debug=True)

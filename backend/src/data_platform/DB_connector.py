import json
import re
import pandas as pd
from sqlalchemy import Engine, text
from src.config import db_settings, transactions_db_settings
from src.utils.spelling import generate_variants
from sqlalchemy.orm import sessionmaker
from src.utils.paths.csv_paths import PATH_TRANSACTIONS
from sqlalchemy import Connection


TAX_ADVISORS_SUBSECTOR_ID: int = 153

# functions below use this engine
engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()


def get_overlaps(acquiror_cib_id, target_cib_id, function_id=0, country_id=0, engine_or_conn: Connection | Engine = engine):
    query_params = {
        'acquiror_cib_id': acquiror_cib_id,
        'target_cib_ids': target_cib_id
    }
    
    if country_id == 0:
        # Global case
        query = text("""
            WITH buyer_sector AS (
                SELECT s.sector_radius_km,
                    s.sector_radius_km * 1000 AS radius_meters,
                    s.sector_radius_km * 1609.34 AS radius_miles_meters
                FROM company_sectors cs
                JOIN sectors s ON cs.sector_id = s.sector_id
                WHERE cs.cib_id = :acquiror_cib_id and s.parent_sector_id is null
            ),
            overlap_km AS (
                SELECT *
                FROM get_overlap_by_acquiror(:acquiror_cib_id,(SELECT radius_meters FROM buyer_sector))
            ),
            overlap_miles AS (
                SELECT *
                FROM get_overlap_by_acquiror(:acquiror_cib_id,(SELECT radius_miles_meters FROM buyer_sector))
            )
            SELECT
                :acquiror_cib_id AS acquiror_cib_id,
                ok.target_cib_id,
                NULL::integer AS function_id,
                0 AS country_id,
                ok.overlap_ratio AS overlap_1,
                ok.overlap_ratio AS overlap_3,
                ok.overlap_ratio AS overlap_5,
                ok.overlap_ratio AS overlap_10,
                ok.overlap_ratio AS overlap_25,
                ok.overlap_ratio AS overlap_100,
                NULL::integer AS relative_density,
                om.overlap_ratio AS overlap_miles_1,
                om.overlap_ratio AS overlap_miles_3,
                om.overlap_ratio AS overlap_miles_5,
                om.overlap_ratio AS overlap_miles_10,
                om.overlap_ratio AS overlap_miles_25,
                om.overlap_ratio AS overlap_miles_100
            FROM overlap_km ok
            JOIN overlap_miles om ON ok.target_cib_id = om.target_cib_id
            WHERE ok.target_cib_id = :target_cib_ids;
        """)
    else:
        # Country-specific case
        query_params['country_id'] = country_id
        query = text("""
            WITH buyer_sector AS (
                SELECT s.sector_radius_km,
                    s.sector_radius_km * 1000 AS radius_meters,
                    s.sector_radius_km * 1609.34 AS radius_miles_meters
                FROM company_sectors cs
                JOIN sectors s ON cs.sector_id = s.sector_id
                WHERE cs.cib_id = :acquiror_cib_id and s.parent_sector_id is null
            ),
            target_country AS (
                SELECT country_id
                FROM countries
                WHERE old_country_id = :country_id
            ),
            overlap_km AS (
                SELECT *
                FROM get_overlap_by_country(:acquiror_cib_id, (SELECT country_id FROM target_country), (SELECT radius_meters FROM buyer_sector))
            ),
            overlap_miles AS (
                SELECT *
                FROM get_overlap_by_country(:acquiror_cib_id, (SELECT country_id FROM target_country), (SELECT radius_miles_meters FROM buyer_sector))
            )
            SELECT
                :acquiror_cib_id AS acquiror_cib_id,
                ok.target_cib_id,
                NULL::integer AS function_id,
                :country_id AS country_id,
                ok.overlap_ratio AS overlap_1,
                ok.overlap_ratio AS overlap_3,
                ok.overlap_ratio AS overlap_5,
                ok.overlap_ratio AS overlap_10,
                ok.overlap_ratio AS overlap_25,
                ok.overlap_ratio AS overlap_100,
                NULL::integer AS relative_density,
                om.overlap_ratio AS overlap_miles_1,
                om.overlap_ratio AS overlap_miles_3,
                om.overlap_ratio AS overlap_miles_5,
                om.overlap_ratio AS overlap_miles_10,
                om.overlap_ratio AS overlap_miles_25,
                om.overlap_ratio AS overlap_miles_100
            FROM overlap_km ok
            JOIN overlap_miles om
            ON ok.target_cib_id = om.target_cib_id
            AND ok.country_id = om.country_id
            WHERE ok.target_cib_id = :target_cib_ids;
        """)

    # Execute the query with parameters
    result = pd.read_sql(query, engine_or_conn, params=query_params)
    return result


def get_overlaps_multiple_targets(acquiror_cib_id, target_cib_ids, function_id=0, country_id=0):
    query_params = {
        'acquiror_cib_id': acquiror_cib_id,
        'target_cib_ids': tuple(target_cib_ids)
    }
    
    if country_id == 0:
        # Global case
        query = text("""
            WITH buyer_sector AS (
                SELECT s.sector_radius_km,
                    s.sector_radius_km * 1000 AS radius_meters,
                    s.sector_radius_km * 1609.34 AS radius_miles_meters
                FROM company_sectors cs
                JOIN sectors s ON cs.sector_id = s.sector_id
                WHERE cs.cib_id = :acquiror_cib_id and s.parent_sector_id is null
            ),
            overlap_km AS (
                SELECT *
                FROM get_overlap_by_acquiror(:acquiror_cib_id,(SELECT radius_meters FROM buyer_sector))
            ),
            overlap_miles AS (
                SELECT *
                FROM get_overlap_by_acquiror(:acquiror_cib_id,(SELECT radius_miles_meters FROM buyer_sector))
            )
            SELECT
                :acquiror_cib_id AS acquiror_cib_id,
                ok.target_cib_id,
                NULL::integer AS function_id,
                0 AS country_id,
                ok.overlap_ratio AS overlap_1,
                ok.overlap_ratio AS overlap_3,
                ok.overlap_ratio AS overlap_5,
                ok.overlap_ratio AS overlap_10,
                ok.overlap_ratio AS overlap_25,
                ok.overlap_ratio AS overlap_100,
                NULL::integer AS relative_density,
                om.overlap_ratio AS overlap_miles_1,
                om.overlap_ratio AS overlap_miles_3,
                om.overlap_ratio AS overlap_miles_5,
                om.overlap_ratio AS overlap_miles_10,
                om.overlap_ratio AS overlap_miles_25,
                om.overlap_ratio AS overlap_miles_100
            FROM overlap_km ok
            JOIN overlap_miles om ON ok.target_cib_id = om.target_cib_id
            WHERE ok.target_cib_id IN :target_cib_ids;
        """)
    else:
        # Country-specific case
        query_params['country_id'] = country_id
        query = text("""
            WITH buyer_sector AS (
                SELECT s.sector_radius_km,
                    s.sector_radius_km * 1000 AS radius_meters,
                    s.sector_radius_km * 1609.34 AS radius_miles_meters
                FROM company_sectors cs
                JOIN sectors s ON cs.sector_id = s.sector_id
                WHERE cs.cib_id = :acquiror_cib_id and s.parent_sector_id is null
            ),
            target_country AS (
                SELECT country_id
                FROM countries
                WHERE old_country_id = :country_id
            ),
            overlap_km AS (
                SELECT *
                FROM get_overlap_by_country(:acquiror_cib_id, (SELECT country_id FROM target_country), (SELECT radius_meters FROM buyer_sector))
            ),
            overlap_miles AS (
                SELECT *
                FROM get_overlap_by_country(:acquiror_cib_id, (SELECT country_id FROM target_country), (SELECT radius_miles_meters FROM buyer_sector))
            )
            SELECT
                :acquiror_cib_id AS acquiror_cib_id,
                ok.target_cib_id,
                NULL::integer AS function_id,
                :country_id AS country_id,
                ok.overlap_ratio AS overlap_1,
                ok.overlap_ratio AS overlap_3,
                ok.overlap_ratio AS overlap_5,
                ok.overlap_ratio AS overlap_10,
                ok.overlap_ratio AS overlap_25,
                ok.overlap_ratio AS overlap_100,
                NULL::integer AS relative_density,
                om.overlap_ratio AS overlap_miles_1,
                om.overlap_ratio AS overlap_miles_3,
                om.overlap_ratio AS overlap_miles_5,
                om.overlap_ratio AS overlap_miles_10,
                om.overlap_ratio AS overlap_miles_25,
                om.overlap_ratio AS overlap_miles_100
            FROM overlap_km ok
            JOIN overlap_miles om
            ON ok.target_cib_id = om.target_cib_id
            AND ok.country_id = om.country_id
            WHERE ok.target_cib_id IN :target_cib_ids;
        """)

    # Execute the query with parameters
    result = pd.read_sql(query, engine, params=query_params)
    return result


def get_multiple_cib_ids(cib_ids):
    query = text("""
        SELECT
            c.cib_id,
            c.company_name,
            c.legal_company_name,
            c.url,
            ld.operation_date AS last_change_date,
            cr.old_country_id as hq_country_id,
            ARRAY_AGG(DISTINCT cs.sector_id) AS sectors
        from companies c
        join logs_data ld ON ld.log_id = c.last_log_id
        LEFT join countries cr on c.hq_country_id = cr.country_id
        join company_sectors cs on cs.cib_id = c.cib_id
        where c.cib_id IN :cib_ids
        GROUP by c.cib_id, c.company_name, c.legal_company_name, c.url, ld.operation_date, cr.old_country_id;
    """)


    result = pd.read_sql(query, engine, params={"cib_ids": tuple(cib_ids)})
    return result



### COMPANIES
# done
def get_by_cib_id(cib_id=None, engine_or_conn=None):
    """
    Returns the company corresponding to the given cib_id, or a random company if cib_id is None.
    Tax advisors are excluded
    """

    if engine_or_conn is None:
        engine_or_conn = engine

    if cib_id is not None:
        # Specific cib_id query
        query = text(f"""
            SELECT
                c.cib_id,
                c.company_name,
                c.legal_company_name,
                c.url,
                ld.operation_date AS last_change_date,
                cr.old_country_id AS hq_country_id,
                ARRAY_AGG(DISTINCT cs.sector_id) AS sectors
            FROM companies c
            JOIN logs_data ld ON ld.log_id = c.last_log_id
            LEFT JOIN countries cr ON c.hq_country_id = cr.country_id
            JOIN company_sectors cs ON cs.cib_id = c.cib_id
            WHERE c.cib_id = :cib_id
            GROUP BY c.cib_id, c.company_name, c.legal_company_name, c.url, ld.operation_date, cr.old_country_id
            -- HAVING BOOL_AND(cs.sector_id != {TAX_ADVISORS_SUBSECTOR_ID})
        """)

        result = pd.read_sql(query, engine_or_conn, params={"cib_id": cib_id})

    else:
        # Random company query
        query = text(f"""
            SELECT
                c.cib_id,
                c.company_name,
                c.legal_company_name,
                c.url,
                ld.operation_date AS last_change_date,
                cr.old_country_id AS hq_country_id,
                ARRAY_AGG(DISTINCT cs.sector_id) AS sectors
            FROM companies c
            JOIN logs_data ld ON ld.log_id = c.last_log_id
            LEFT JOIN countries cr ON c.hq_country_id = cr.country_id
            JOIN company_sectors cs ON cs.cib_id = c.cib_id
            WHERE c.cib_id NOT LIKE 'samplecompany%'
            GROUP BY c.cib_id, c.company_name, c.legal_company_name, c.url, ld.operation_date, cr.old_country_id
            HAVING BOOL_AND(cs.sector_id != {TAX_ADVISORS_SUBSECTOR_ID})
            ORDER BY RANDOM()
            LIMIT 1
        """)

        result = pd.read_sql(query, engine)

    return result



# NOTE: Possibly to be retired
def get_sector_default_overlap_2(sector_id):
    query = text("""
        SELECT 'overlap_' || s.sector_radius_km AS default_overlap_2
        FROM sectors s
        WHERE s.sector_id = :sector_id;
    """)

    result = pd.read_sql(query, engine, params={"sector_id": sector_id})

    if result.empty:
        return None
    return result.iloc[0]['default_overlap_2']

# done
def get_toplevel_sectors(cib_id):
    query = text("""
        SELECT s.sector_id, s.sector_name
        FROM company_sectors cs
        JOIN sectors s on cs.sector_id = s.sector_id
        WHERE cs.cib_id = :cib_id and s.parent_sector_id is null;
    """)

    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result

# done
def get_sub_sectors(cib_id):
    query = text("""    
        SELECT s.sector_id, s.sector_name
        FROM company_sectors cs
        JOIN sectors s ON s.sector_id = cs.sector_id
        WHERE cs.cib_id = :cib_id AND s.parent_sector_id is not null;
    """)

    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result

# done
def get_toplevel_sector(sector_id):
    query = text("""
        WITH RECURSIVE top_level_sector AS (
            SELECT sector_id, sector_name, parent_sector_id
            FROM sectors
            WHERE sector_id = :sector_id
            
            UNION ALL
            
            SELECT s.sector_id, s.sector_name, s.parent_sector_id
            FROM sectors s
            JOIN top_level_sector tls ON s.sector_id = tls.parent_sector_id
        )
        SELECT sector_id, sector_name
        FROM top_level_sector
        WHERE parent_sector_id is null;
    """)

    result = pd.read_sql(query, engine, params={"sector_id": sector_id})
    return result

# done
def get_children_sectors(cib_id, parent_id):
    query = text("""
        SELECT DISTINCT s.sector_id, s.sector_name
        FROM sectors s
        JOIN company_sectors cs ON s.sector_id = cs.sector_id
        WHERE cs.cib_id = :cib_id AND s.parent_sector_id = :parent_id;
    """)

    result = pd.read_sql(query, engine, params={"cib_id": cib_id, "parent_id": parent_id})
    return result


def search_all_companies(start_string, page, page_size, allow_tax_advisors: bool = False):
    search_variants = generate_variants(start_string)

    # Optional filter for tax advisors
    tax_advisor_filter = ""
    if not allow_tax_advisors:
        tax_advisor_filter = f"""
            AND NOT EXISTS (
                SELECT 1 FROM company_sectors cs2
                WHERE cs2.cib_id = c.cib_id AND cs2.sector_id = {TAX_ADVISORS_SUBSECTOR_ID}
            )
        """

    # Base query
    base_query = f"""
        SELECT DISTINCT ON (c.cib_id)
            c.cib_id, 
            c.company_name, 
            c.legal_company_name, 
            c.url, 
            ld.operation_date AS last_change_date, 
            cr.old_country_id as hq_country_id, 
            s.sector_id
        FROM companies c
        LEFT JOIN logs_data ld ON ld.log_id = c.last_log_id
        LEFT JOIN countries cr ON c.hq_country_id = cr.country_id
        JOIN company_sectors cs ON cs.cib_id = c.cib_id
        JOIN sectors s ON cs.sector_id = s.sector_id
        WHERE s.parent_sector_id IS NULL
        AND c.company_name ILIKE ANY(:search_variants) AND c.cib_id <> 'placeholder.com' AND c.cib_id NOT ILIKE 'samplecompany%'
        {tax_advisor_filter}
    """

    # Prepare query and params based on pagination
    if page == 0:
        query = text(base_query)
        params = {"search_variants": [f"%{variant}%" for variant in search_variants]}
    else:
        query = text(f"""
            {base_query}
            LIMIT :page_size OFFSET :offset
        """)
        offset = (page - 1) * page_size
        params = {
            "search_variants": [f"%{variant}%" for variant in search_variants],
            "page_size": page_size,
            "offset": offset
        }

    # Execute
    result = pd.read_sql(query, engine, params=params)
    return result

    # base_query = """
    # SELECT *
    # FROM company_with_sectors
    # WHERE company_name ILIKE ANY(:search_variants)
    # AND sector_id IN (SELECT sector_id FROM sectors WHERE parent_sector_id IS NULL)
    # """

    # Modify query based on pagination
    if page == 0:
        query = text(base_query)
        params = {"search_variants": [f"%{variant}%" for variant in search_variants]}
    else:
        query = text(f"""
            {base_query}
            LIMIT :page_size OFFSET :offset
        """)
        offset = (page - 1) * page_size
        params = {
            "search_variants": [f"%{variant}%" for variant in search_variants],
            "page_size": page_size,
            "offset": offset
        }

    # Execute the query with parameters
    result = pd.read_sql(query, engine, params=params)
    return result

# done
def get_company_locations(cib_id, country_id=None):
    base_query = """
        SELECT 
            c.cib_id,
            c.company_name,
            l.location_id,
            l.location_name,
            le.city,
            cr.old_country_id as country_id,
            le.lat,
            le.long,
            COALESCE(json_agg(lf.function_id) FILTER (WHERE lf.function_id IS NOT NULL), '[]') AS Functions
        FROM locations l
        JOIN companies c ON l.cib_id = c.cib_id
        left join countries cr on cr.country_id = l.country_id
        LEFT JOIN location_extras le on l.location_id = le.location_id
        LEFT JOIN location_functions lf ON l.location_id = lf.location_id
        WHERE c.cib_id = :cib_id
    """

    # Only add the country condition if a country_id is provided.
    if country_id is not None:
        base_query += " AND cr.old_country_id = :country_id"

    base_query += """
        GROUP BY c.cib_id, c.company_name, l.location_id, l.location_name, le.city, cr.old_country_id, le.lat, le.long;
    """

    params = {"cib_id": cib_id}
    if country_id is not None:
        params["country_id"] = country_id

    result = pd.read_sql(text(base_query), engine, params=params)
    return result

# done

def get_multiple_companies_locations(cib_ids, country_id=None):
    base_query = """
        SELECT
            c.cib_id,
            c.company_name,
            l.location_id,
            l.location_name,
            le.city,
            cr.old_country_id as country_id,
            le.lat,
            le.long,
            COALESCE(json_agg(lf.function_id) FILTER (WHERE lf.function_id IS NOT NULL), '[]') AS Functions
        FROM locations l
        JOIN companies c ON l.cib_id = c.cib_id
        LEFT JOIN countries cr on cr.country_id = l.country_id
        LEFT JOIN location_extras le on l.location_id = le.location_id
        LEFT JOIN location_functions lf ON l.location_id = lf.location_id
        WHERE c.cib_id IN :cib_ids
    """

    params = {"cib_ids": tuple(cib_ids)}

    if country_id is not None and country_id != 0:
        base_query += " AND cr.old_country_id = :country_id"
        params["country_id"] = country_id

    base_query += """
        GROUP BY c.cib_id, c.company_name, l.location_id, l.location_name, le.city, cr.old_country_id, le.lat, le.long
    """

    result = pd.read_sql(text(base_query), engine, params=params)
    return result

#done


def get_available_country_combinations(acquiror_cib_id, target_cib_id):
    # Parameterized query with placeholders
    base_query = text("""
        SELECT 
            c.cib_id AS acquiror_cib_id,
            c2.cib_id AS target_cib_id,
            json_agg(DISTINCT cr.old_country_id) AS available_countries
        FROM (SELECT * from companies where cib_id = :acquiror_cib_id) c 
        CROSS JOIN (SELECT * from companies where cib_id = :target_cib_id) c2
        JOIN locations l ON l.cib_id = c.cib_id OR l.cib_id = c2.cib_id
        join countries cr on l.country_id = cr.country_id
        WHERE c.cib_id <> c2.cib_id
        GROUP BY c.cib_id, c2.cib_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(base_query, engine, params={
        "acquiror_cib_id": acquiror_cib_id,
        "target_cib_id": target_cib_id
    })

    return result

# done
def get_functional_combinations(acquiror_cib_id, target_cib_id):
    from sqlalchemy import text  # Import `text` for safer parameterized queries


    # Parameterized query with placeholders
    base_query = text("""
        WITH company_1_functions AS (
            SELECT DISTINCT lf.function_id
            FROM locations l
            JOIN location_functions lf ON l.location_id = lf.location_id
            WHERE l.cib_id = :acquiror_cib_id
        ),
        company_2_functions AS (
            SELECT DISTINCT lf.function_id
            FROM locations l
            JOIN location_functions lf ON l.location_id = lf.location_id
            WHERE l.cib_id = :target_cib_id
        )
        SELECT 
            :acquiror_cib_id AS acquiror_cib_id,
            :target_cib_id AS target_cib_id,
            json_agg(DISTINCT cf.function_id) AS common_functions
        FROM company_1_functions cf
        JOIN company_2_functions cf2 ON cf.function_id = cf2.function_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(base_query, engine, params={
        "acquiror_cib_id": acquiror_cib_id,
        "target_cib_id": target_cib_id
    })

    return result

# done
def get_functional_combinations_with_countries(acquiror_cib_id, target_cib_id):

    # Parameterized query with placeholders
    base_query = text("""
        SELECT 
          c.cib_id AS acquiror_cib_id,
          c2.cib_id AS target_cib_id,
          lf.function_id,
          json_agg(DISTINCT cr.old_country_id) AS available_countries
        FROM (SELECT * from companies where cib_id = :acquiror_cib_id) c 
        CROSS JOIN (SELECT * from companies where cib_id = :target_cib_id) c2
        JOIN locations l ON l.cib_id = c.cib_id OR l.cib_id = c2.cib_id
        JOIN location_functions lf ON l.location_id = lf.location_id
        join countries cr on l.country_id = cr.country_id
        WHERE c.cib_id <> c2.cib_id
        GROUP BY c.cib_id, c2.cib_id, lf.function_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(base_query, engine, params={
        "acquiror_cib_id": acquiror_cib_id,
        "target_cib_id": target_cib_id
    })

    return result

# done
def get_functional_geo_availables(cib_id):
    # Parameterized query with placeholders
    query = text("""
        SELECT 
            l.cib_id, 
            lf.function_id AS function, 
            json_agg(DISTINCT cr.old_country_id) AS countries
        FROM locations l
        JOIN location_functions lf ON l.location_id = lf.location_id
        join countries cr on l.country_id = cr.country_id
        WHERE l.cib_id = :cib_id
        GROUP BY l.cib_id, lf.function_id
        ORDER BY l.cib_id, lf.function_id;
    """)

    # Execute the query with parameters
    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result


def get_companies_with_countries(cib_ids: list[str], engine_or_conn=None):
    if engine_or_conn is None:
        engine_or_conn = engine
    
    query = text("""
        SELECT
        c.cib_id,
        c.company_name,
        ARRAY_AGG(DISTINCT ct.country_name ORDER BY ct.country_name) AS country_list
        FROM company_sectors AS cs
        JOIN companies       AS c  ON cs.cib_id      = c.cib_id
        JOIN locations       AS l  ON l.cib_id       = c.cib_id
        JOIN countries       AS ct ON ct.country_id  = l.country_id
        WHERE c.cib_id IN :cib_ids
        GROUP BY
            c.cib_id,
            c.company_name;
        """)
    result = pd.read_sql(query, engine_or_conn, params={"cib_ids": tuple(cib_ids)})
    return result


# done
def get_company_countries_by_locations(cib_id):
    query = text("""
        SELECT
            cr.old_country_id as country_id,
            COUNT(l.location_id) AS total_locations
        FROM locations l
        join countries cr on l.country_id = cr.country_id
        WHERE l.cib_id = :cib_id
        GROUP BY cr.old_country_id
        ORDER BY total_locations DESC;
        """)

    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result

# done
def get_company_functions(cib_id):
    # Parameterized query
    query = text("""
        SELECT 
            l.cib_id, 
            c.company_name, 
            lf.function_id, 
            COUNT(l.location_id) AS number_of_locations
        FROM 
            locations l
        JOIN 
            companies c ON l.cib_id = c.cib_id
        JOIN 
            location_functions lf ON l.location_id = lf.location_id
        WHERE 
            l.cib_id = :cib_id
        GROUP BY 
            l.cib_id, 
            c.company_name, 
            lf.function_id
        ORDER BY 
            number_of_locations DESC;
    """)

    # Execute the query with the parameter
    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result


# needs testing
def get_locations_count(conn, cib_id: str) -> int:
    query = text("""
        SELECT COUNT(l.location_id) AS number_of_locations
        FROM locations l
        WHERE l.cib_id = :cib_id
    """)

    n_locations: int = conn.execute(query, parameters={"cib_id": cib_id}).scalar_one()
    return n_locations


def get_locations_by_cib_id_simple(cib_ids, country_id=None):

    # Use parameterized placeholders for `cib_ids`
    ids_placeholder = ', '.join([f":id_{i}" for i in range(len(cib_ids))])

    query = f"""
    SELECT * 
    FROM locations l
    """

    # Add optional `country_id` filter using a parameterized query
    if country_id:
        query += f"""
        JOIN countries cr on cr.country_id = l.country_id
        WHERE l.cib_id IN ({ids_placeholder})
        AND cr.old_country_id = :country_id
        """
    else:
        query += f"""
        WHERE l.cib_id IN ({ids_placeholder})
        """

    # Build the parameter dictionary
    params = {f"id_{i}": id_ for i, id_ in enumerate(cib_ids)}
    if country_id:
        params["country_id"] = country_id

    # Execute the query with the parameterized inputs
    df = pd.read_sql(text(query), engine, params=params)

    return df

# needs testing
def get_locations_by_ids(location_ids, country_id=None):

    ids_placeholder = ', '.join([f":id_{i}" for i in range(len(location_ids))])

    query = f"""
    SELECT * 
    FROM locations l
    """

    if country_id:
        query += f"""
        JOIN countries cr on cr.country_id = l.country_id
        WHERE l.location_id IN ({ids_placeholder})
        AND cr.old_country_id = :country_id
        """
    else:
        query += f"""
        WHERE l.location_id IN ({ids_placeholder})
        """

    # Build parameter dictionary
    params = {f"id_{i}": id_ for i, id_ in enumerate(location_ids)}
    if country_id:
        params["country_id"] = country_id

    # Execute the query safely with parameters
    df = pd.read_sql(text(query), engine, params=params)

    return df

# done
def get_all_table_unsafe(table_name):
    #    result = pd.read_sql(f"SELECT * FROM {table_name}", db_settings.ENGINE_MANAGER.get_or_create())
    if table_name == 'countries':
        query = text("""
            SELECT
                old_country_id as country_id,
                country_name,
                continent_id,
                country_id as country_code,
                country_area
            FROM countries c
            ORDER BY old_country_id ASC;
        """)
        result = pd.read_sql(query, engine)
    elif table_name == 'sectors':
        query = text("""
            SELECT 
                s.sector_id, 
                s.sector_name, 
                s.parent_sector_id, 
                'overlap_' || s.sector_radius_km as default_overlap_1,
                'overlap_' || s.sector_radius_km as default_overlap_2
            FROM sectors s
            ORDER BY s.sector_id ASC;
        """)
        result = pd.read_sql(query, engine)
    else:
        with engine.connect() as conn:
            result = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
    
    return result

# done
def get_all_table(table_name):
    # from sqlalchemy import Table, MetaData
    #
    # engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()
    # metadata = MetaData()
    # metadata.reflect(bind=engine)
    #
    # if table_name not in metadata.tables:
    #     raise ValueError(f"Invalid table name: {table_name}")
    #
    # table = Table(table_name, metadata, autoload_with=engine)
    # query = table.select()
    #
    # result = pd.read_sql(query, engine)
    return get_all_table_unsafe(table_name)

# done
def get_all_regions():
    query = """
        select 
            cr.old_country_id as country_id, 
            r.region_name, 
            '' as region_type 
        from regions r
        join countries cr on cr.country_id = r.country_id;
        """
    result = pd.read_sql(query, engine)
    return result

# done

def target_search_no_buyer(continent_id: int, country_id: int, sector_id: int, region_name: str = None) -> pd.DataFrame:
    continent_id = int(continent_id)
    country_id = int(country_id)
    sector_id = int(sector_id)
    
    main_country_cutoff = 0.1

    query_params = {
        "continent_id": continent_id,
        "country_id": country_id,
        "sector_id": sector_id,
        "region_name": region_name,
        "main_country_cutoff": main_country_cutoff
    }

    # Determine geography logic
    # 4 cases in total:
    # - Global search (continent_id == 0, country_id == 0, region_id == None)
    # - Continent-wide search (continent_id != 0, country_id == 0, region_id == None)
    # - Country-wide search (continent_id != 0, country_id != 0, region_id == None)
    # - Region-wide search (continent_id != 0, country_id != 0, region_id != None)
    # API for no-buyer swaps '-' to None automatically

    match (continent_id != 0, country_id != 0, bool(region_name)):
        case (True, True, True):                                    # Region-wide search
            geo_cte = """geo_filter AS (
                    SELECT region_id AS geo_id
                    FROM regions
                    WHERE region_name = :region_name
                ),"""
            geo_condition = "AND l.region_id = (SELECT geo_id FROM geo_filter)"
        case (True, True, False):                                   # Country-wide search
            geo_cte = """geo_filter AS (
                    SELECT country_id AS geo_id
                    FROM countries
                    WHERE old_country_id = :country_id
                ),"""
            geo_condition = "AND l.country_id = (SELECT geo_id FROM geo_filter)"
        case (True, False, _):                                      # Continent-wide search
            geo_cte = """geo_filter AS (
                    SELECT continent_id AS geo_id
                    FROM continents
                    WHERE continent_id = :continent_id
                ),"""
            geo_condition = """AND l.country_id IN (
                    SELECT country_id FROM countries
                    WHERE continent_id = (SELECT geo_id FROM geo_filter)
                )"""
        case _:                                                     # Global search
            geo_cte = ""
            geo_condition = ""

    # Final query
    query = f"""
        WITH {geo_cte}
        target_location_distribution AS (
            SELECT
                l.cib_id,
                COUNT(*) FILTER (WHERE TRUE {geo_condition})::float / COUNT(*) AS geo_ratio
            FROM locations l
            GROUP BY l.cib_id
        )
        SELECT 
            c.cib_id AS target_cib_id,
            c.cib_id AS cib_id,
            cr.old_country_id AS country_id,
            c.company_name,
            cs.sector_id AS target_sector_id,
            COUNT(l.location_id) AS locations_count
        FROM companies c
        JOIN company_sectors cs ON c.cib_id = cs.cib_id
        JOIN locations l ON c.cib_id = l.cib_id
        LEFT JOIN countries cr ON cr.country_id = c.hq_country_id
        JOIN target_location_distribution tld ON tld.cib_id = c.cib_id
        WHERE cs.sector_id = :sector_id
          {geo_condition}
          AND tld.geo_ratio >= :main_country_cutoff
        GROUP BY c.cib_id, c.company_name, cs.sector_id, cr.old_country_id
        ORDER BY locations_count DESC
        LIMIT 10000;
    """

    return pd.read_sql(text(query), engine, params=query_params)


def get_sub_sectors(sector_id, level, max_depth):
    query = text("""
        WITH RECURSIVE sector_hierarchy AS (
            SELECT 
                sector_id, 
                sector_name, 
                parent_sector_id,
                :start_level AS level
            FROM sectors
            WHERE sector_id = :start_sector

            UNION ALL

            SELECT 
                s.sector_id, 
                s.sector_name, 
                s.parent_sector_id,
                sh.level + 1 AS level
            FROM sectors s
            INNER JOIN sector_hierarchy sh ON s.parent_sector_id = sh.sector_id
            WHERE sh.level < :max_depth
        )
        SELECT * FROM sector_hierarchy;
    """)

    params = {
        'start_sector': sector_id,  # Starting point sector_id
        'start_level': level,  # Starting level
        'max_depth': max_depth  # Maximum recursion depth
    }

    # Execute the query with parameters
    df = pd.read_sql(query, engine, params=params)
    return df

# done
def get_locations_within_range(lat, long, radius):
    # Parameterized query
    query = text("""
        SELECT 
            l.location_id,
            l.cib_id,
            l.location_name,
            l.full_address,
            le.street,
            le.city,
            cr.old_country_id as country_id,
            le.lat,
            le.long
        FROM locations l
        JOIN location_extras on l.location_id = le.location_id
        JOIN countries cr on cr.country_id = l.country_id
        WHERE ST_DWithin(
                l.coordinates, 
                ST_SetSRID(ST_MakePoint(:long, :lat), 4326)::geography, 
                :radius
        );
    """)

    # Execute the query with parameters
    params = {
        "lat": lat,
        "long": long,
        "radius": radius
    }
    result = pd.read_sql(query, engine, params=params)
    return result


def get_country_name(country_id: int) -> str:
    query = "SELECT country_name FROM countries WHERE old_country_id = :country_id"

    with engine.connect() as conn:
        result = conn.execute(text(query), {"country_id": country_id})

        try:
            country = result.one()[0]
        except Exception as e:
            raise ValueError(f"Country with id '{country_id}' not found.") from e
        return country

### TARGET_SEARCH
# done
def get_names_by_cib_ids(cib_ids):
    if not cib_ids:
        return pd.DataFrame(columns=['cib_id', 'company_name'])

    query = text("""
        SELECT 
            c.cib_id, 
            c.company_name
        FROM companies c
        WHERE c.cib_id IN :cib_ids
        ORDER BY c.cib_id;
    """)

    # Execute the query with the cib_ids parameter
    # Convert list to tuple to ensure compatibility with SQL IN clause
    result = pd.read_sql(query, engine, params={"cib_ids": tuple(cib_ids)})

    return result





# done
def get_location_source(cib_id):
    query = text("""
        SELECT 
            l.cib_id,
            l.location_id AS most_recent_location_id, 
            l.location_name, 
            l.full_address, 
            s.source_link, 
            s.source_type,
            s.date_added
        FROM locations l
        JOIN location_extras le on le.location_id = l.location_id
        JOIN sourcelinks s ON le.source_id = s.source_id
        WHERE l.cib_id = :cib_id
        ORDER BY s.date_added DESC
        LIMIT 1;
    """)

    result = pd.read_sql(query, engine, params={"cib_id": cib_id})
    return result

# done
def get_locations_for_country_sector(country_id, sector_id):
    # Parameterized query
    query = text("""
        SELECT l.*
        FROM locations l
        JOIN countries cr on l.country_id = cr.country_id
        JOIN company_sectors mc ON l.cib_id = cs.cib_id
        WHERE cr.old_country_id = :country_id AND cs.sector_id = :sector_id
    """)

    # Execute the query with parameters
    params = {
        "country_id": country_id,
        "sector_id": sector_id
    }
    result = pd.read_sql(query, engine, params=params)
    return result

# done
def get_isochrones(location_id, travel_time):

    # Validate and sanitize `travel_time`
    valid_travel_times = {"5", "10", "15", "30", "60"}  # Add acceptable travel time values
    if str(travel_time) not in valid_travel_times:
        raise ValueError(f"Invalid travel_time: {travel_time}")

    # Use parameterized query for `location_id`
    query = text(f"""
        SELECT 
            i.location_id,
            ST_AsGeoJSON(i.isochrone_{travel_time}) AS isochrone
        FROM isochrones i
        WHERE i.location_id = :location_id
    """)

    # Execute the query safely
    result = pd.read_sql(query, engine, params={"location_id": location_id})

    # Convert the results to GeoJSON format
    features = []
    for _, row in result.iterrows():
        geometry = json.loads(row['isochrone'])
        feature = {
            "type": "Feature",
            "geometry": geometry
        }
        features.append(feature)

    geojson_data = {
        "type": "FeatureCollection",
        "features": features
    }

    return geojson_data

# done
def get_country_geometry(country_id):
    query = text(f"""
        SELECT 
            cm.country_id,
            ST_AsGeoJSON(cm.geometry) AS geometry
        FROM country_geometry cm
        JOIN countries cr on cr.country_id = cm.country_id
        WHERE cr.old_country_id = :country_id
    """)

    result = pd.read_sql(query, engine, params={"country_id": country_id})

    features = []
    for _, row in result.iterrows():
        geometry = json.loads(row['geometry'])
        feature = {
            "type": "Feature",
            "geometry": geometry
        }
        features.append(feature)

    geojson_data = {
        "type": "FeatureCollection",
        "features": features
    }

    return geojson_data

# done
def get_region(region_id=None, country_id=None, with_geometry=False):
    if region_id:
        where_clause = "WHERE r.region_code = :region_id"
        params = {"region_id": region_id}
    elif country_id:
        where_clause = "WHERE cr.old_country_id = :country_id"
        params = {"country_id": country_id}
    else:
        raise ValueError("Either region_id or country_id must be provided.")


    if with_geometry:
        query = text(f"""
            SELECT 
                r.region_code as region_id,
                cr.old_country_id as country_id,
                ST_AsGeoJSON(r.geometry) AS geometry
            FROM regions r
            JOIN countries cr on cr.country_id = r.country_id
            {where_clause}
        """)
    else:
        query = text(f"""
            SELECT 
                r.region_code as region_id,
                cr.old_country_id as country_id
            FROM regions r
            JOIN countries cr on cr.country_id = r.country_id
            {where_clause}
        """)

    result = pd.read_sql(query, engine, params=params)

    if with_geometry:
        # Convert to GeoJSON
        features = [
            {
                "type": "Feature",
                "geometry": json.loads(row["geometry"]),
                "properties": {k: v for k, v in row.items() if k not in ["geometry"]},
            }
            for _, row in result.iterrows()
        ]
        return {"type": "FeatureCollection", "features": features}

    return result  # Return as a DataFrame if with_geometry=False

# done
def get_region_enriched(region_id=None, country_id=None, with_geometry=False):
    if region_id:
        where_clause = "WHERE r.region_code = :region_id"
        params = {"region_id": region_id}
    elif country_id:
        where_clause = "WHERE cr.old_country_id = :country_id"
        params = {"country_id": country_id}
    else:
        raise ValueError("Either region_id or country_id must be provided.")

    if with_geometry:
        query = text(f"""
            SELECT 
                r.region_code as region_id,
                cr.old_country_id as country_id,
                r.gdp_mil as gdp,
                r.population,
                r.average_income,
                r.population_density,
                ST_AsGeoJSON(r.geometry) AS geometry
            FROM regions r
            JOIN countries cr on cr.country_id = r.country_id
            {where_clause}
        """)
    else:
        query = text(f"""
            SELECT 
                r.region_code as region_id,
                cr.old_country_id as country_id,
                r.gdp_mil as gdp,
                r.population,
                r.average_income,
                r.population_density
            FROM regions r
            JOIN countries cr on cr.country_id = r.country_id
            {where_clause}
        """)

    result = pd.read_sql(query, engine, params=params)

    if with_geometry:
        # Convert to GeoJSON
        features = [
            {
                "type": "Feature",
                "geometry": json.loads(row["geometry"]),
                "properties": {k: v for k, v in row.items() if k not in ["geometry"]},
            }
            for _, row in result.iterrows()
        ]
        return {"type": "FeatureCollection", "features": features}

    return result  # Return as a DataFrame if with_geometry=False

# not used anymore, to delete
def get_geojson_country(country_id=None):
    if not country_id:
        raise ValueError("Country_id must be provided.")
        
    query = text("""SELECT geojson FROM country_regions_geojson WHERE country_id = :country_id;""")
    params = {"country_id": country_id}
    result = pd.read_sql(query, engine, params=params)

    return result


# transactions DB
engine_manager_transactions = transactions_db_settings.ENGINE_MANAGER


def get_transaction_presentations():
    colnames = ['TotalSynergiesAbs', 'RevenueSynAbs', 'CostSynergiesAbs', 'TaxSynAbs',
                      'implementation_period', 'integration_cost_abs','FundSynAbs','CapexSynAbs']
    engine: Engine = engine_manager_transactions.get_or_create()
    query = f"""
        SELECT DISTINCT ON (s.deal_id, s.column_name) s.deal_id AS transaction_id, s.column_name, s.link,
        f.downloaded_From as download_link,
            (CASE WHEN f.file_id IS NOT NULL THEN True 
                ELSE False END) AS has_content
        FROM sourcelinks s
        LEFT JOIN files f ON s.link LIKE f.link || '%'
        WHERE s.column_name IN :column_name
        --ORDER BY has_content, deal_id ASC;
    """
    result = pd.read_sql(text(query), engine, params={"column_name": tuple(colnames)})
    return result

def generate_label(transaction_id:str, field:str)->str|None:
    '''Accepts a row (as a tuple) from the db and generates a verbose link / label'''
    engine: Engine = engine_manager_transactions.get_or_create()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        result = session.execute(
            text("""
                SELECT t."AcquirorClearName", f.document_type, f.document_date, s.page
                FROM sourcelinks as s
                LEFT JOIN files as f
                ON s.link LIKE f.link || '%'
                LEFT JOIN transactions as t
                ON t."DealID" = s.deal_id
                WHERE s.deal_id = :transaction_id AND s.column_name = :column_name
            """),
            {'transaction_id': transaction_id, 'column_name': field}
        ).fetchone()
        print(result)
        if not result:
            return None

        # Generate verbose label
        acquirer,document_type,document_date,page = result
        if document_date == None or acquirer == None or document_type == None:
            return None
        
        # If needed, change the input to correct wording
        categories = {
            'transcript':'investor call webcast',
            'regulatory':'other regulatory filing',
            'news':'news article'                
            }
        form = document_type.lower()
        if document_type in categories:
            form = categories[form]

        formatted_date = document_date.strftime("%B %#d, %Y")
        if page != None:
            if isinstance(page, str):
                match = re.search(r'\d+', page)  # Find the first number in the string
                page = match.group() if match else ''
            res_string = f"{acquirer.title()} {form}, {formatted_date}, page {page}"
        else:
            res_string = f"{acquirer.title()} {form}, {formatted_date}"
            
        return res_string
    
def generate_label_all(ids:list)->pd.DataFrame:
    '''Accepts deal ids and column names, returns a dataframe with labels for all pairs'''
    colnames = ['TotalSynergiesAbs', 'RevenueSynAbs', 'CostSynergiesAbs', 'TaxSynAbs',
                      'implementation_period', 'integration_cost_abs','FundSynAbs','CapexSynAbs']
    categories = {
                'transcript':'investor call webcast',
                'regulatory':'other regulatory filing',
                'news':'news article',                
                }
    calculations = {
        'CostSynergiesPerc':['CostSynergiesAbs','ltm_target_cost','Run rate cost synergies','Target operating cost'],
        'TotalSynergiesPerc':['TotalSynergiesAbs','ltm_target_revenue','Total run rate synergies','Target operating income'],
        'RevenueSynPerc':['RevenueSynAbs','ltm_target_revenue','Run rate revenue synergies','Target operating income'],
        'FundSynergiesPerc':['FundSynAbs','ltm_target_revenue','Run rate funding synergies','Target operating income'],
        'integration_cost_percentage':['integration_cost_abs','TotalSynergiesAbs','Integration cost','Total run rate synergies'],
    }
    calculated_cols = list(calculations.keys())
    df1 = pd.DataFrame({'DealID':ids})
    df2 = pd.DataFrame({'column_name':colnames+calculated_cols})
    left_df = pd.merge(left=df1, right=df2, how='cross') #contains all pairs

    engine: Engine = engine_manager_transactions.get_or_create()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        result = session.execute(
            text("""
                SELECT DISTINCT ON (s.deal_id, s.column_name) s.deal_id, s.column_name, t."AcquirorClearName", f.document_type, f.document_date, s.page
                FROM sourcelinks as s
                JOIN files as f
                ON s.link LIKE f.link || '%'
                LEFT JOIN transactions as t
                ON t."DealID" = s.deal_id
                WHERE s.deal_id IN :ids AND s.column_name IN :columns
            """),
            {'ids': tuple(ids), 'columns': tuple(colnames)}
        ).fetchall()
        #print(result)
        if not result:
            df = left_df
            df['label'] = None
            return df

        labels = []
        # Generate verbose label
        for row in result:
            dealid,column_name,acquirer,document_type,document_date,page = row
            if document_date == None or acquirer == None or document_type == None:
                labels.append((dealid,column_name,None))
            else:
                form = document_type.lower()
                if form in categories:
                    form = categories[form]

                formatted_date = document_date.strftime("%B %#d, %Y")
                if form == 'news article':
                    res_string = f"News article, {formatted_date}"
                else:
                    if page != None:
                        if isinstance(page, str):
                            match = re.search(r'\d+', page)  # Find the first number in the string
                            page = match.group() if match else ''
                        res_string = f"{acquirer.title()} {form}, {formatted_date}, page {page}"
                    else:
                        res_string = f"{acquirer.title()} {form}, {formatted_date}"
                labels.append((dealid,column_name,res_string))
    # Check if any dealid - column pair is missing, if yes add it with a blank label

    df = pd.DataFrame(labels, columns=['DealID','column_name','label'])

    # Part 2: Add percentage calculation labels
    data = pd.read_csv(filepath_or_buffer=PATH_TRANSACTIONS)
    data = data[data['DealID'].isin(ids)].drop_duplicates(subset='DealID')
    for x in calculations:
        data[x] = None
        mask = (data[calculations[x][0]].notna()) & (data[calculations[x][1]].notna())
        if not data[mask].empty:
            data.loc[mask,x] = data.loc[mask].apply(lambda row: f'{calculations[x][2]} USD {round(row[calculations[x][0]])}mm / {calculations[x][3]} USD {row[calculations[x][1]]}mm', axis=1)
        if x == 'CostSynergiesPerc':
            data['opcostsrc'] = data['opcostsrc'].fillna('').astype(str)
            data.loc[mask,x] += data.loc[mask,'opcostsrc']
    data = data[['DealID']+calculated_cols]
    data = pd.melt(data, id_vars=['DealID'], value_vars=calculated_cols, var_name='column_name', value_name='label').reset_index()[['DealID','column_name','label']]

    df = pd.concat([df,data])
    df = pd.merge(left = left_df, right = df, on=['DealID', 'column_name'], how='left')
    return df
    
def get_presentation_by_id(transaction_id:str, field:str='Investor presentation'):
    
    engine: Engine = engine_manager_transactions.get_or_create()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        result = session.execute(
            text("""
                SELECT file
                FROM sourcelinks s
                JOIN files f
                ON s.link LIKE f.link || '%'
                WHERE s.deal_id = :transaction_id AND s.column_name = :column_name
            """),
            {'transaction_id': transaction_id, 'column_name': field}
        ).fetchone()

        return result[0] if result else None


from typing import Optional
from sqlalchemy import Engine, create_engine
import threading
from sqlalchemy.engine.url import URL

class EngineManager:
    engine: Optional[Engine]
    lock: threading.Lock

    def __init__(self, connection_url: URL):
        """
        Initialize the EngineManager with a connection URL.

        Args:
            connection_url (str): The database connection URL.
        """
        self.engine = None
        self.connection_url = connection_url
        self.lock = threading.Lock()

    def get_or_create(self) -> Engine:
        """
        Get the existing engine or create a new one if it doesn't exist.

        Returns:
            Engine: The SQLAlchemy Engine instance.
        """
        if self.engine is None:
            with self.lock:
                if self.engine is None:
                    self.engine = create_engine(self.connection_url)
        return self.engine

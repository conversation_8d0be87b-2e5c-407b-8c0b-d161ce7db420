import geopandas as gpd
from sqlalchemy import Engine, text
from src.config import db_settings


def get_isochrones_for_locations(location_ids, cib_ids, country_id, travel_time):
    engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()

    def get_locations_query(location_ids, cib_ids, country_id, isochrone):
        # Prepare placeholders for the query
        location_id_placeholder = ', '.join(f"'{location_id}'::UUID" for location_id in location_ids)
        cib_id_placeholder = ', '.join(f"'{cib_id}'" for cib_id in cib_ids)

        # Construct the query
        query = f"""
        WITH location_list AS (
            SELECT UNNEST(ARRAY[
                {location_id_placeholder}
            ]::UUID[]) AS location_id
        ),
        cib_id_list AS (
            SELECT UNNEST(ARRAY[
                {cib_id_placeholder}
            ]) AS cib_id
        )
        SELECT DISTINCT 
            l.location_id,
            l.cib_id,
            l.location_name,
            l.full_address,
            l.lat,
            l.long,
            i.isochrone_{isochrone} as isochrone
        FROM 
            locations l
        JOIN 
            company_sectors cs ON l.cib_id = cs.cib_id
        JOIN 
            isochrones i ON l.location_id = i.location_id
        WHERE 
            (l.location_id IN (
                SELECT location_id FROM location_list
            ) 
            OR 
            l.cib_id IN (
                SELECT cib_id FROM cib_id_list
            ))
            AND l.country_id = {country_id};
        """
        return query

    query = text(get_locations_query(location_ids, cib_ids, country_id, travel_time))

    result = gpd.read_postgis(query, engine, geom_col='isochrone')

    result['location_id'] = result['location_id'].apply(str)

    return result
from src.config import db_settings
import pandas as pd
from sqlalchemy import Engine, text


engine: Engine = db_settings.ENGINE_MANAGER.get_or_create()


def consolidate(buyer_cib_id, sector_id, continent_id, country_id, lower_limit, upper_limit, region_name):
    country_id = int(country_id)
    continent_id = int(continent_id)

    main_country_percentage = 0.1

    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'sector_id': sector_id,
        'lower_size_limit': lower_limit,
        'upper_size_limit': upper_limit,
        'continent_id': continent_id,
        'country_id': country_id,
        'main_country_cutoff': main_country_percentage
    }

    # 4 cases in total:
    # - Global search (continent_id == 0, country_id == 0, region_id == '-')
    # - Continent-wide search (continent_id != 0, country_id == 0, region_id == '-')
    # - Country-wide search (continent_id != 0, country_id != 0, region_id == '-')
    # - Region-wide search (continent_id != 0, country_id != 0, region_id != '-')

    match (continent_id != 0, country_id != 0, region_name != '-'):
        case (True, True, True):                                    # Region-wide search
            query_params['region_name'] = region_name
            geo_cte = """target_geo AS (
                    SELECT region_id AS geo_id
                    FROM regions
                    WHERE region_name = :region_name
                ),"""
            overlap_call = """SELECT * FROM get_overlap_by_region(
                    :buyer_cib_id,
                    (SELECT geo_id FROM target_geo),
                    (SELECT distance_meters FROM buyer_sector)
                )"""
            acq_loc_filter = "AND l.region_id = (SELECT geo_id FROM target_geo)"
            geo_select = ":country_id AS country_id"
        case (True, True, False):                                   # Country-wide search
            geo_cte = """target_geo AS (
                    SELECT country_id AS geo_id
                    FROM countries
                    WHERE old_country_id = :country_id
                ),"""
            overlap_call = """SELECT * FROM get_overlap_by_country(
                    :buyer_cib_id,
                    (SELECT geo_id FROM target_geo),
                    (SELECT distance_meters FROM buyer_sector)
                )"""
            acq_loc_filter = "AND l.country_id = (SELECT geo_id FROM target_geo)"
            geo_select = ":country_id AS country_id"
        case (True, False, _):                                      # Continent-wide search
            geo_cte = """target_geo AS (
                    SELECT continent_id AS geo_id
                    FROM continents
                    WHERE continent_id = :continent_id
                ),"""
            overlap_call = """SELECT * FROM get_overlap_by_continent(
                    :buyer_cib_id,
                    (SELECT geo_id FROM target_geo),
                    (SELECT distance_meters FROM buyer_sector)
                )"""
            acq_loc_filter = """AND country_id IN (
                    SELECT country_id FROM countries WHERE continent_id = (SELECT geo_id FROM target_geo)
                )"""
            geo_select = "0 AS country_id, :continent_id AS continent_id"
        case _:                                                     # Global search
            geo_cte = ""
            overlap_call = """SELECT * FROM get_overlap_by_acquiror(
                    :buyer_cib_id,
                    (SELECT distance_meters FROM buyer_sector)
                )"""
            acq_loc_filter = ""
            geo_select = "0 AS country_id"

    # Final query
    query = f"""
        WITH
        {geo_cte}
        buyer_sector AS (
            SELECT
                s.sector_radius_km * 1000 AS distance_meters,
                s.sector_id,
                COUNT(l.location_id) AS total_locations,
                (COUNT(l.location_id) * :lower_size_limit)::int as lower_limit,
                (COUNT(l.location_id) * :upper_size_limit)::int as upper_limit
            FROM company_sectors cs
            JOIN sectors s ON cs.sector_id = s.sector_id
            JOIN locations l ON l.cib_id = cs.cib_id
            WHERE cs.cib_id = :buyer_cib_id AND s.parent_sector_id is null {acq_loc_filter}
            GROUP BY s.sector_radius_km, s.sector_id
        ),
        matching_target_cibs AS (
            SELECT cs.cib_id, cs.sector_id
            FROM company_sectors cs
            WHERE cs.sector_id = :sector_id
        ),
        overlap_data AS (
            {overlap_call}
        ),
        target_location_distribution AS (
            SELECT
                l.cib_id,
                COUNT(*) FILTER (
                    where TRUE {acq_loc_filter}
                )::float / COUNT(*) AS geo_ratio
            FROM locations l
            join overlap_data od on od.target_cib_id = l.cib_id
            GROUP BY l.cib_id
        )
        SELECT
            :buyer_cib_id AS acquiror_cib_id,
            mtc.sector_id AS target_sector_id,
            od.target_cib_id,
            {geo_select},
            od.total_locations AS locations_count,
            od.overlap_ratio AS overlap
        FROM overlap_data od
        JOIN matching_target_cibs mtc ON od.target_cib_id = mtc.cib_id
        JOIN target_location_distribution tld ON tld.cib_id = od.target_cib_id
        CROSS JOIN buyer_sector bs
        WHERE od.overlap_ratio >= 0.25
            AND od.total_locations > :lower_size_limit
            AND od.total_locations <= :upper_size_limit
            AND tld.geo_ratio >= :main_country_cutoff
        ORDER BY overlap DESC, locations_count DESC;
    """

    return pd.read_sql(text(query), engine, params=query_params)



def expand_adjacent(buyer_cib_id, sector_id, lower_limit, upper_limit):
    # main_area_query = ''
    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'sector_id': sector_id,
        'lower_size_limit': lower_limit,
        'upper_size_limit': upper_limit
    }

    query = text(f"""
        WITH buyer_sector AS (
            SELECT
                s.sector_radius_km * 1000 AS distance_meters,
                s.sector_id,
                (COUNT(l.location_id) * :lower_size_limit)::int as lower_limit,
                (COUNT(l.location_id) * :upper_size_limit)::int as upper_limit
            FROM company_sectors cs
            JOIN sectors s ON cs.sector_id = s.sector_id
            JOIN locations l ON l.cib_id = cs.cib_id
            WHERE cs.cib_id = :buyer_cib_id AND s.parent_sector_id is null
            GROUP BY s.sector_radius_km, s.sector_id
        ),
        matching_target_cibs AS (
            SELECT cs.cib_id, cs.sector_id
            FROM company_sectors cs
            WHERE cs.sector_id = :sector_id
        ),
        overlap_data AS (
            SELECT *
            FROM get_overlap_by_acquiror(:buyer_cib_id, (SELECT distance_meters FROM buyer_sector))
        ),
        inclusion_index_data AS (
            SELECT *
            FROM get_inclusion_index_main_country(
                :buyer_cib_id, 
                (SELECT sector_id FROM buyer_sector),
                (SELECT lower_limit FROM buyer_sector),
                (SELECT upper_limit FROM buyer_sector)
            )
        )
        SELECT
            :buyer_cib_id AS acquiror_cib_id,
            mtc.sector_id AS target_sector_id,
            mtc.cib_id AS target_cib_id,
            0 AS country_id,
            iid.total_locations AS locations_count,
            od.overlap_ratio AS overlap,
            iid.inclusion_index AS inclusion_index,
            NULL::integer AS relative_density
        FROM matching_target_cibs mtc
        JOIN overlap_data od ON od.target_cib_id = mtc.cib_id
        JOIN inclusion_index_data iid ON iid.target_cib_id = mtc.cib_id
        where od.overlap_ratio < 0.25
        ORDER by iid.inclusion_index DESC, iid.total_locations DESC
        -- LIMIT 500;
    """)

    result = pd.read_sql(query, engine, params=query_params)
    return result



def expand_new_countries(buyer_cib_id, sector_id, continent_id, country_id, lower_limit, upper_limit, region_name):
    country_id = int(country_id)
    continent_id = int(continent_id)

    main_country_percentage = 0.1

    query_params = {
        'buyer_cib_id': buyer_cib_id,
        'sector_id': sector_id,
        'lower_size_limit': lower_limit,
        'upper_size_limit': upper_limit,
        'continent_id': continent_id,
        'country_id': country_id,
        'main_country_cutoff': main_country_percentage
    }

    # 4 cases in total:
    # - Global search (continent_id == 0, country_id == 0, region_id == '-')
    # - Continent-wide search (continent_id != 0, country_id == 0, region_id == '-')
    # - Country-wide search (continent_id != 0, country_id != 0, region_id == '-')
    # - Region-wide search (continent_id != 0, country_id != 0, region_id != '-')

    match (continent_id != 0, country_id != 0, region_name != '-'):
        case (True, True, True):                                    # Region-wide search
            query_params['region_name'] = region_name
            geo_cte = """target_geo AS (
                    SELECT region_id AS geo_id
                    FROM regions
                    WHERE region_name = :region_name
                ),"""
            buyer_size_filter = "AND l.region_id = (SELECT geo_id FROM target_geo)"
            target_group_filter = "WHERE l.region_id = (SELECT geo_id FROM target_geo)"
            geo_select = ":country_id AS country_id"
        case (True, True, False):                                   # Country-wide search
            geo_cte = """target_geo AS (
                    SELECT country_id AS geo_id
                    FROM countries
                    WHERE old_country_id = :country_id
                ),"""
            buyer_size_filter = "AND l.country_id = (SELECT geo_id FROM target_geo)"
            target_group_filter = "WHERE l.country_id = (SELECT geo_id FROM target_geo)"
            geo_select = ":country_id AS country_id"
        case (True, False, _):                                      # Continent-wide search
            geo_cte = """target_geo AS (
                    SELECT continent_id AS geo_id
                    FROM continents
                    WHERE continent_id = :continent_id
                ),"""
            buyer_size_filter = """AND l.country_id IN (
                    SELECT country_id FROM countries
                    WHERE continent_id = (SELECT geo_id FROM target_geo)
                )"""
            target_group_filter = """WHERE l.country_id IN (
                    SELECT country_id FROM countries
                    WHERE continent_id = (SELECT geo_id FROM target_geo)
                )"""
            geo_select = "0 AS country_id, :continent_id AS continent_id"
        case _:                                                     # Global search
            geo_cte = ""
            buyer_size_filter = ""
            target_group_filter = ""
            geo_select = "0 AS country_id"

    query = f"""
        WITH
        {geo_cte}
        buyer_size AS (
            SELECT
                COUNT(*) * :lower_size_limit AS lower_size_limit,
                COUNT(*) * :upper_size_limit AS upper_size_limit
            FROM locations l
            WHERE cib_id = :buyer_cib_id {buyer_size_filter}
        ),
        matching_target_cibs AS (
            SELECT cs.cib_id
            FROM company_sectors cs
            JOIN sectors s ON cs.sector_id = s.sector_id
            WHERE s.sector_id = :sector_id
        ),
        target_counts AS (
            SELECT l.cib_id, COUNT(*) AS loc_count
            FROM locations l
            {target_group_filter}
            GROUP BY l.cib_id
        ),
        filtered_targets AS (
            SELECT tc.cib_id
            FROM target_counts tc
            JOIN buyer_size bs ON tc.loc_count > bs.lower_size_limit AND tc.loc_count <= bs.upper_size_limit
        ),
        target_location_distribution AS (
            SELECT
                l.cib_id,
                COUNT(*) FILTER (
                    WHERE TRUE {buyer_size_filter}
                )::float / COUNT(*) AS geo_ratio
            FROM locations l
            JOIN filtered_targets ft ON l.cib_id = ft.cib_id
            GROUP BY l.cib_id
        )
        SELECT
            l.cib_id AS target_cib_id,
            :sector_id AS target_sector_id,
            {geo_select},
            COUNT(*) AS locations_count
        FROM locations l
        JOIN filtered_targets ft ON l.cib_id = ft.cib_id
        JOIN target_location_distribution tld ON l.cib_id = tld.cib_id
        WHERE l.cib_id IN (SELECT cib_id FROM matching_target_cibs)
          AND l.cib_id <> :buyer_cib_id
          AND tld.geo_ratio >= :main_country_cutoff
        GROUP BY l.cib_id
        ORDER BY locations_count DESC
    """

    return pd.read_sql(text(query), engine, params=query_params)
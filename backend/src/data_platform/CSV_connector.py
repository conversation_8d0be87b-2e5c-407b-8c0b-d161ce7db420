import pandas as pd
from src.utils.paths.csv_paths import PATH_TRANSACTIONS, PATH_SECTORS2MAP, REGION_HEATMAP_LABELS, REGION_HEATMAP_CATEGORIES
from src.analysis.transactions import lite_version_df, transaction_ids_by_sector
transactions = pd.read_csv(filepath_or_buffer=PATH_TRANSACTIONS)
transactions['Year'] = pd.to_numeric(transactions['Year'])
#transactions = pd.DataFrame()

sectors2map = pd.read_csv(filepath_or_buffer=PATH_SECTORS2MAP, sep=';')

region_heatmap_labels = pd.read_csv(filepath_or_buffer=REGION_HEATMAP_LABELS, sep=';')
#region_heatmap_labels.drop(columns=['Unnamed: 0'], inplace=True)

region_heatmap_categories = pd.read_csv(filepath_or_buffer=REGION_HEATMAP_CATEGORIES, sep=';')

def get_transactions(sector = '', only_with_total_synergies = False, company_name_search = '', lite = False):
    """
    Retrieve filtered and sorted transaction data.

    Args:
        sector (str): The sector to filter transactions by. Defaults to '' (no filtering).
        last_n (int): The number of most recent transactions to return. Defaults to 99999 (all transactions).
        only_with_total_synergies (bool): If True, only transactions with non-empty 'TotalSynergiesAbs' are returned. Defaults to False.

    Returns:
        pd.DataFrame: The filtered and sorted transaction data.
    """
    optional_filtered_transactions = transactions.copy()
    if lite: # If lite, use helper columns to remove data which is connected to a transcript
        optional_filtered_transactions = lite_version_df(optional_filtered_transactions)

    if only_with_total_synergies:
        optional_filtered_transactions = optional_filtered_transactions[optional_filtered_transactions['TotalSynergiesAbs'].notna()]
    
    if sector:
        allowed_ids = transaction_ids_by_sector(sector)
        optional_filtered_transactions = optional_filtered_transactions[optional_filtered_transactions['DealID'].isin(allowed_ids)]

    if company_name_search:
        optional_filtered_transactions = optional_filtered_transactions[
            optional_filtered_transactions['AcquirorClearName'].str.contains(company_name_search, case=False, na=False) |
            optional_filtered_transactions['TargetClearName'].str.contains(company_name_search, case=False, na=False)
            ]
    if lite:
        sorting_cols = ['syn_quant_lite','metrics_lite','Year']
    else:
        sorting_cols = ['syn_quant','metrics','Year']
    
    sorted_transactions = optional_filtered_transactions.sort_values(by=sorting_cols, ascending=[False,False,False])
    #print(sorted_transactions[sorting_cols])
    return sorted_transactions


def get_region_heatmap_labels(country_id, data_type = "CATEGORY"):

    if data_type == "LABEL":
        return region_heatmap_labels[region_heatmap_labels['country_id'] == country_id]
    elif data_type == "CATEGORY":
        return region_heatmap_categories[region_heatmap_categories["country_id"] == country_id]
    else:
        raise ValueError("Invalid data type. Choose either 'CATEGORY' or 'LABEL'.")



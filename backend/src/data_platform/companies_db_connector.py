from src.config import markup_db_settings
import pandas as pd
from sqlalchemy import Engine, text
import re
import unicodedata


engine: Engine = markup_db_settings.ENGINE_MANAGER.get_or_create()


def _clean_keyword(s: str) -> str:
    if not s:
        return ""
    s = unicodedata.normalize("NFKC", s).strip()
    s = re.sub(r"\s+", " ", s).strip("'`\"")
    return s[:200]  # guardrail

def _word_boundary_regex(word: str) -> str:
    esc = re.sub(r'([\\.^$|(){}\[\]*+?])', r'\\\1', word)
    return rf"(?i)[[:<:]]{esc}[[:>:]]"

def get_occurrences_ranking(
    keyword: str,
    mode: str = "stem",              # "stem" (slower, uses positions tsvector) or "exact" (faster, prefilter with tsvector, search with regex)
    lang: str = "en",                # "en" or "de"
    limit: int = 100,
    fallback_union: bool = True,     # if chosen lang yields nothing, try EN||DE
    cfg_en: str = "english_unaccent",
    cfg_de: str = "german_unaccent",
) -> pd.DataFrame:
    kw = _clean_keyword(keyword)
    if not kw:
        print("Keyword is empty after cleaning.")
        return pd.DataFrame()

    
    cfg = cfg_de if lang == "de" else cfg_en

    def _run_stem(config: str) -> pd.DataFrame:
        sql = text("""
          WITH lex AS (
            SELECT array_agg(m[1]) AS lemmata
            FROM regexp_matches(
                  (plainto_tsquery(CAST(:config AS regconfig), :kw))::text,
                  $$'([^' ]+)'$$, 'g'
                ) AS m
          ),
          cand AS (
            SELECT base_url, chunk_tsv_pos::text AS tv
            FROM test_markdown_chunks
            WHERE chunk_tsv @@ plainto_tsquery(CAST(:config AS regconfig), :kw)
          ),
          expanded AS (
            -- one regexp per (chunk, lexeme); capture the positions string
            SELECT
              c.base_url,
              (regexp_match(
                c.tv,
                '''' || l.lex || ''':\\s*([0-9A-D,]+)',   -- <-- quote + capture group
                'i'
              ))[1] AS poslist
            FROM cand c
            CROSS JOIN LATERAL unnest(
              COALESCE((SELECT lemmata FROM lex), ARRAY[]::text[])
            ) AS l(lex)
          )
          SELECT
            base_url,
            SUM(
              COALESCE(
                array_length(
                  regexp_split_to_array(poslist, '\\s*,\\s*'),
                  1
                ),
                0
              )
            ) AS occurrences
          FROM expanded
          GROUP BY base_url
          ORDER BY occurrences DESC
          LIMIT :limit;
        """)
        return pd.read_sql(sql, engine, params={"kw": kw, "config": config, "limit": limit})

    def _run_stem_union() -> pd.DataFrame:
        sql = text("""
          WITH lex AS (
            SELECT ARRAY_AGG(DISTINCT lex) AS lemmata
            FROM (
              SELECT (m)[1] AS lex
              FROM regexp_matches(
                (plainto_tsquery(CAST(:cfg_en AS regconfig), :kw))::text, $$'([^' ]+)'$$, 'g') m
              UNION
              SELECT (m)[1] AS lex
              FROM regexp_matches(
                (plainto_tsquery(CAST(:cfg_de AS regconfig), :kw))::text, $$'([^' ]+)'$$, 'g') m
            ) x
          ),
          cand AS (
            SELECT base_url, chunk_tsv_pos::text AS tv
            FROM test_markdown_chunks
            WHERE chunk_tsv @@ (
              plainto_tsquery(CAST(:cfg_en AS regconfig), :kw)
              ||
              plainto_tsquery(CAST(:cfg_de AS regconfig), :kw)
            )
          ),
          expanded AS (
            SELECT
              c.base_url,
              (regexp_match(
                c.tv,
                '''' || l.lex || ''':\\s*([0-9A-D,]+)',   -- <-- quote + capture group
                'i'
              ))[1] AS poslist
            FROM cand c
            CROSS JOIN LATERAL unnest(
              COALESCE((SELECT lemmata FROM lex), ARRAY[]::text[])
            ) AS l(lex)
          )
          SELECT
            base_url,
            SUM(
              COALESCE(
                array_length(
                  regexp_split_to_array(poslist, '\\s*,\\s*'),
                  1
                ),
                0
              )
            ) AS occurrences
          FROM expanded
          GROUP BY base_url
          ORDER BY occurrences DESC
          LIMIT :limit;
        """)
        return pd.read_sql(sql, engine, params={"kw": kw, "cfg_en": cfg_en, "cfg_de": cfg_de, "limit": limit})

    def _run_exact(config: str) -> pd.DataFrame:
        rx = _word_boundary_regex(kw)
        sql = text("""
            WITH cand AS (
              SELECT base_url, chunk
              FROM test_markdown_chunks
              WHERE chunk_tsv @@ plainto_tsquery(CAST(:config AS regconfig), :kw)
            )
            SELECT base_url,
                   SUM( (SELECT COUNT(*) FROM regexp_matches(chunk, :rx, 'g')) ) AS occurrences
            FROM cand
            GROUP BY base_url
            ORDER BY occurrences DESC
            LIMIT :limit;
        """)
        return pd.read_sql(sql, engine, params={"kw": kw, "config": config, "rx": rx, "limit": limit})

    def _run_exact_union() -> pd.DataFrame:
        print("union")
        rx = _word_boundary_regex(kw)
        sql = text("""
            WITH cand AS (
              SELECT base_url, chunk
              FROM test_markdown_chunks
              WHERE chunk_tsv @@ (
                plainto_tsquery(CAST(:cfg_en AS regconfig), :kw)
                ||
                plainto_tsquery(CAST(:cfg_de AS regconfig), :kw)
              )
            )
            SELECT base_url,
                   SUM( (SELECT COUNT(*) FROM regexp_matches(chunk, :rx, 'g')) ) AS occurrences
            FROM cand
            GROUP BY base_url
            ORDER BY occurrences DESC
            LIMIT :limit;
        """)
        return pd.read_sql(sql, engine, params={"kw": kw, "cfg_en": cfg_en, "cfg_de": cfg_de, "rx": rx, "limit": limit})

    # Run chosen single-config first (faster). Fallback to union if empty and allowed.
    if mode == "exact":
        df = _run_exact(cfg)
        if fallback_union and df.empty:
            df = _run_exact_union()
        return df

    # stem (inflections, plurals; slower)
    df = _run_stem(cfg)
    if fallback_union and df.empty:
        df = _run_stem_union()
    return df



def get_occurences_text(base_url: str, keyword: str, lang: str = "en", cfg_en: str = "english_unaccent", cfg_de: str = "german_unaccent",) -> pd.DataFrame:
    kw = _clean_keyword(keyword)
    cfg = cfg_de if lang == "de" else cfg_en
    limit = 100
    
    params={"config": cfg, "limit": limit, "base_url": base_url}
    
    query = text(f"""
      WITH q AS (
        SELECT websearch_to_tsquery(:config, $q${kw}$q$) AS ts
      ),
      hits AS (
        SELECT base_url, operation_id, chunk_no, chunk,
              ts_rank_cd(chunk_tsv, q.ts) AS rank
        FROM test_markdown_chunks, q
        WHERE base_url = :base_url and chunk_tsv @@ q.ts
        ORDER BY rank DESC
        LIMIT :limit
      )
      SELECT distinct on (snippet)
      base_url, operation_id, chunk_no, rank,
            ts_headline(
              :config,
              chunk, q.ts,
              'StartSel=<b>, StopSel=</b>, MaxFragments=3, MinWords=4, MaxWords=25, FragmentDelimiter= … '
            ) AS snippet
      FROM hits, q
      ORDER BY snippet, rank DESC;
    """)

    result = pd.read_sql(query, engine, params=params)
    
    return result
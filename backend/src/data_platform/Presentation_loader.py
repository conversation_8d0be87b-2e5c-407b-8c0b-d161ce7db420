from enum import Enum
import yaml


import src.utils.paths.csv_paths as csv_paths

class ResultType(Enum):
    FULL_YAML = "full yaml",
    TOPLEVEL_YAML_SECTION = "toplevel yaml section",
    CONFIG_BY_SECTOR_ID = "config by sector id"

with open(csv_paths.PATH_YAML_CONFIG, 'r') as file:
    data = yaml.safe_load(file)  # Recommended for security



def get_default_sector_config(data):
    for config in data.get('Sector_configurations', []):
        if 'Default' in config:
            return config['Default']  # Return the 'Default' configuration directly
    return {"error": "No default configuration found."}

def get_result_by_type(result_type, param):
    if result_type == ResultType.FULL_YAML:
        return data
    elif result_type == ResultType.TOPLEVEL_YAML_SECTION:
        return data[param] if param in data else {"error": f"No such section: {param}"}
    elif result_type == ResultType.CONFIG_BY_SECTOR_ID:
        for config in data.get('Sector_configurations', []):
            for key, value in config.items():
                param = int(param)
                if 'Sector_ids' in value and param in value['Sector_ids']:
                    return value  # Return only the matching config
        return get_default_sector_config(data)
    else:
        raise ValueError(f"Invalid result type: {result_type}")



#x = get_result_by_type(ResultType.CONFIG_BY_SECTOR_ID, 41)
#print(x)

#print(data['Sector_configurations'][0])
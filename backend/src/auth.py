from flask_httpauth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uth
from functools import wraps
from flask import g



auth = HTTPTokenAuth(scheme='Bearer')

TOKENS = {
    "cloud-ib123": {"user": "cloud-ib", "role": "superuser"},
    "cloud-ib-8084": {"user": "cloud-ib", "role": "user"},  # technow
    "cloud-ib-pptx-8084": {"user": "powerpoint", "role": "user"},
    "cloud-ib-ai-8084": {"user": "ai", "role": "user"},
}


@auth.verify_token
def verify_token(token):
    if token in TOKENS:
        g.token = token  # Store the token in Flask's context
        g.user = TOKENS[token]['user']  # Store the user information in Flask's context
        return TOKENS[token]
    return None


def role_required(required_role):
    def decorator(fn):
        @wraps(fn)
        @auth.login_required
        def wrapper(*args, **kwargs):
            user_info = auth.current_user()
            if user_info['role'] != required_role:
                return {"msg": "Access forbidden: insufficient role."}
            print("ok")
            return fn(*args, **kwargs)
        return wrapper
    return decorator

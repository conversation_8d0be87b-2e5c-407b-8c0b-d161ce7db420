import logging
import psycopg2
from flask import g, has_request_context
from sqlalchemy import URL

class PostgresLogHandler(logging.Handler):
    def __init__(self, url: URL):
        super().__init__()
        self.conn = psycopg2.connect(database=url.database, user=url.username, password=url.password, host=url.host, port=url.port)
        self.cursor = self.conn.cursor()

    def emit(self, record):
        try:
            # user = getattr(g, 'user', 'N/A') if has_request_context() else 'N/A'
            token = getattr(g, 'token', 'NA') if has_request_context() else 'NA'
            
            log_entry = self.format(record)
            sql = """
            INSERT INTO data_platform_logs (created_at, level, token, message, logger_name, pathname, func_name, line_no)
            VALUES (CURRENT_TIMESTAMP, %s, %s, %s, %s, %s, %s, %s)
            """
            self.cursor.execute(
                sql,
                (
                    record.levelname,
                    token,
                    log_entry,
                    record.name,
                    record.pathname,
                    record.funcName,
                    record.lineno
                )
            )
            self.conn.commit()
        except Exception as e:
            print(f"Failed to log to Postgres: {e}")

    def close(self):
        self.conn.close()
        super().close()

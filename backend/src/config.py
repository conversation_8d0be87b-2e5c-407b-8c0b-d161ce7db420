from typing import TypeVar

from dotenv import load_dotenv
from pydantic import computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict
from sqlalchemy import URL
from src.data_platform.engine_manager import EngineManager

load_dotenv(override=True)


T = TypeVar("T", bound="CommonSettings")


class CommonSettings(BaseSettings):
    model_config = SettingsConfigDict(env_ignore_empty=True, extra="ignore")

    @classmethod
    def with_prefix(cls: type[T], env_prefix: str = "") -> T:
        model_config = SettingsConfigDict(env_prefix=env_prefix)
        return type(cls.__name__, (cls,), {"model_config": model_config})()  # type: ignore


class Settings(CommonSettings):
    PORT: int
    USE_DB_LOGGER: bool = False


class DatabaseSettings(CommonSettings):
    DATABASE: str
    USER: str
    PASSWORD: str
    HOST: str
    PORT: int

    @computed_field
    @property
    def URL(self) -> URL:
        return URL(
            drivername="postgresql",
            username=self.USER,
            password=self.PASSWORD,
            host=self.HOST,
            port=self.PORT,
            database=self.DATABASE,
            query={
                'sslmode': 'require',
                'client_encoding': 'utf8'},  # type: ignore
        )
    
    @computed_field
    @property
    def ENGINE_MANAGER(self) -> EngineManager:
        return EngineManager(connection_url=self.URL)


settings = Settings()  # type: ignore
db_settings = DatabaseSettings.with_prefix("POSTGRES_")  # type: ignore
rep_db_settings = DatabaseSettings.with_prefix("REP_POSTGRES_") # type: ignore
log_db_settings = DatabaseSettings.with_prefix("LOG_")  # type: ignore
transactions_db_settings = DatabaseSettings.with_prefix("TRANSACTIONS_")  # type: ignore
markup_db_settings = DatabaseSettings.with_prefix("MARKUP_")  # type: ignore


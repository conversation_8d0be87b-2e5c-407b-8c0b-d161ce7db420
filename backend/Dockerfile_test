FROM python:3.11

# Install Nginx
RUN apt-get update && apt-get install -y nginx

# Set environment variables to ensure Python outputs logs to the console
ENV PYTHONUNBUFFERED 1


# Set the working directory
WORKDIR /app

# Copy the entire project into the container
COPY . /app
RUN pip install --no-cache-dir -r requirements.txt

#CMD ["python", "-u", "src/app.py"]

# should switch to this later on
# CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8000", "src.production:production"]



# Remove default nginx site
RUN rm /etc/nginx/sites-enabled/default

# Copy custom nginx configuration to the container
COPY nginx.conf.test /etc/nginx/sites-enabled/nginx.conf

# Expose the necessary ports
EXPOSE 80
EXPOSE 443
EXPOSE 5000
EXPOSE 9100

# Set permissions for the log directories
RUN mkdir -p /var/log/gunicorn && chmod -R 755 /var/log/gunicorn



# Command to start both Nginx and Flask using Gunicorn
#CMD service nginx start && gunicorn --bind 0.0.0.0:5000 \
#    --access-logfile /var/log/gunicorn/access.log \
#    --error-logfile /var/log/gunicorn/error.log \
#    src.app:app

#
CMD gunicorn src.app:app \
    --bind 0.0.0.0:5000 \
    --access-logfile /var/log/gunicorn/access.log \
    --error-logfile /var/log/gunicorn/error.log & \
    --timeout 90 & \
    nginx -g 'daemon off;'


#CMD gunicorn --bind 0.0.0.0:5000 \
#    src.app:app

services:
  branch-nginx-proxy:
    image: nginxproxy/nginx-proxy
    container_name: nginx-proxy
    ports:
      - 80:80
      - 443:443
    restart: unless-stopped
    volumes:
      - certs:/etc/nginx/certs:ro
      - html:/usr/share/nginx/html:rw
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - dataplatform

  branch-nginx-proxy-acme:
    image: nginxproxy/acme-companion
    container_name: nginx-proxy-acme
    restart: unless-stopped
    volumes_from:
      - branch-nginx-proxy
    volumes:
      - certs:/etc/nginx/certs:rw
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - acme:/etc/acme.sh:rw
    networks:
      - dataplatform

volumes:
  certs:
    name: branch-deploy-certs
  html:
    name: branch-deploy-html
  acme:
    name: branch-deploy-acme

networks:
  dataplatform:
    driver: bridge
    name: dataplatform
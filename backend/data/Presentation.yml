Allowed_sectors:
  Target_search:
    Sector_ids: [1, 2, 3]

Sector_configurations:
  - Name: 'Population_location_based_sectors'
    Sector_ids: [4, 54, 57, 58, 16, 64, 60, 63, 34, 68, 77, 78, 37, 20, 41, 7, 19, 22, 66, 14, 151, 26, 43, 44, 45, 46, 47, 48, 49]
    Snapshot_view:
      Maps:
        - Name: 'Location overlap'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: company_overlaps
          Indicator_API_diff_sector: company_overlaps
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Wealth management data AUM'
          show_only_for_sector: [41]
          show_only_for_country: [185, 184]
          note: 'Show AUM numbers for US wealth managers only, country = US 185 and sector id  = 41'
          API_same_sector: wealth_management_data_aum
          API_diff_sector: wealth_management_data_aum
          show_for_strategies: [ CONSOLIDATE, EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT ]
        - Name: 'Wealth management data AUM per client'
          show_only_for_sector: [41]
          show_only_for_country: [185]
          note: 'Show AUM numbers for US wealth managers only, country = US 185 and sector id  = 41'
          API_same_sector: wealth_management_data_aum_client
          API_diff_sector: wealth_management_data_aum_client
          show_for_strategies: [ CONSOLIDATE, EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT ]
        - Name: 'Wealth management data num clients'
          show_only_for_sector: [41]
          show_only_for_country: [185]
          note: 'Show AUM numbers for US wealth managers only, country = US 185 and sector id  = 41'
          API_same_sector: wealth_management_data_num_clients
          API_diff_sector: wealth_management_data_num_clients
          show_for_strategies: [ CONSOLIDATE, EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT ]
        - Name: 'WM Ownership'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185 ]
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'WM Client focus'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185 ]
          API_same_sector: strip_profile_client_focus_short
          API_diff_sector: strip_profile_client_focus_short
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'WM Intl clients'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185 ]
          API_same_sector: wealth_management_data_intl_clients
          API_diff_sector: wealth_management_data_intl_clients
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'WM Intln locations'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185 ]
          API_same_sector: wealth_management_data_intl_locations
          API_diff_sector: wealth_management_data_intl_locations
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'WM Total employees'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185, 63 ]
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'WM Advisory employees'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 185 ]
          API_same_sector: strip_profile_advisory_employees
          API_diff_sector: strip_profile_advisory_employees
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Banks total assets'
          show_only_for_sector: [ 7 ]
          show_only_for_country: [ 185 ]
          API_same_sector: banks_total_assets
          API_diff_sector: banks_total_assets
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Banks net interest income'
          show_only_for_sector: [ 7 ]
          show_only_for_country: [ 185 ]
          API_same_sector: banks_net_interest_income
          API_diff_sector: banks_net_interest_income
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Banks non interest income'
          show_only_for_sector: [ 7 ]
          show_only_for_country: [ 185 ]
          API_same_sector: banks_non_interest_income
          API_diff_sector: banks_non_interest_income
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Banks net income'
          show_only_for_sector: [ 7 ]
          show_only_for_country: [ 185 ]
          API_same_sector: banks_net_income
          API_diff_sector: banks_net_income
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Company overlaps'
          API_same_sector: company_overlaps
          API_diff_sector: company_overlaps
          show_for_strategies: [CONSOLIDATE]
        - Name: 'Population location'
          API_same_sector: population_location_increase
          API_diff_sector: population_location_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
        - Name: 'Incremental income'
          API_same_sector: incremental_income
          API_diff_sector: incremental_income
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
        - Name: 'Employee data'
          show_only_for_sector: [ 22, 150, 151, 152 ]
          show_only_for_country: [ 63 ]
          note: 'Only for German insurance brokers'
          API_same_sector: insurance_brokers_data_employees
          API_diff_sector: insurance_brokers_data_employees
          show_for_strategies: [ CONSOLIDATE, EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT ]
        - Name: 'Employee data German WM'
          show_only_for_sector: [ 41 ]
          show_only_for_country: [ 63 ]
          note: 'Only for German WMs'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [ CONSOLIDATE, EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT ]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Company overlaps'
          EXPAND_NEW_COUNTRIES: 'Population location'
          EXPAND_ADJACENT: 'Population location'

  - Name: 'Population_country_based_sectors'
    Sector_ids: [27]
    Snapshot_view:
      Maps:
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: True
          Show_dots: False
          Indicator_API_same_sector: population_country_increase
          Indicator_API_diff_sector: population_country_increase
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: False
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
    Target_search:
      Columns:
        - Name: 'Population country'
          API_same_sector: population_country_increase
          API_diff_sector: population_country_increase
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Population country'
          EXPAND_NEW_COUNTRIES: 'Population country'
          EXPAND_ADJACENT: 'Population country'

  - Name: 'Population_country_based_sectors'
    Sector_ids: [13, 55, 15, 17, 62, 79, 42, 61, 30, 36, 38]
    Snapshot_view:
      Maps:
        - Name: 'Population'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: population_country_increase
          Indicator_API_diff_sector: population_country_increase
    Target_search:
      Columns:
        - Name: 'Population country'
          API_same_sector: population_country_increase
          API_diff_sector: population_country_increase
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Population country'
          EXPAND_NEW_COUNTRIES: 'Population country'
          EXPAND_ADJACENT: 'Population country'
  - Name: 'GDP_country_based_sectors'
    Sector_ids: [5, 40, 50, 2, 6, 8, 10, 53, 11, 12, 56, 24, 29, 67, 69, 74, 35, 38, 97, 32, 98, 99, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120]
    Snapshot_view:
      Maps:
        - Name: 'Origination reach increase'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: gdp_country_increase
          Indicator_API_diff_sector: gdp_country_increase
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
    Target_search:
      Columns:
        - Name: 'GDP country'
          API_same_sector: gdp_country_increase
          API_diff_sector: gdp_country_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [gdp, income]
        Indicator:
          CONSOLIDATE: 'GDP country'
          EXPAND_NEW_COUNTRIES: 'GDP country'
          EXPAND_ADJACENT: 'GDP country'

  - Name: 'GDP_location_based_sectors'
    Sector_ids: [1, 21, 81, 150, 152]
    Snapshot_view:
      Maps:
        - Name: 'Location overlap'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: company_overlaps
          Indicator_API_diff_sector: company_overlaps
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: gdp_location_increase
          Indicator_API_diff_sector: gdp_location_increase
    Target_search:
      Columns:
        - Name: 'GDP location'
          API_same_sector: gdp_location_increase
          API_diff_sector: gdp_location_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
        - Name: 'Employee data'
          show_only_for_sector: [ 22, 150, 151, 152 ]
          show_only_for_country: [ 63 ]
          note: 'Only for German insurance brokers'
          API_same_sector: insurance_brokers_data_employees
          API_diff_sector: insurance_brokers_data_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [gdp, income]
        Indicator:
          CONSOLIDATE: 'GDP location'
          EXPAND_NEW_COUNTRIES: 'GDP location'
          EXPAND_ADJACENT: 'GDP location'

  - Name: 'Wealth_country_based_sectors'
    Sector_ids: [83, 82, 95]
    Snapshot_view:
      Maps:
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
        - Name: 'Wealth'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: wealth_country_increase
          Indicator_API_diff_sector: wealth_country_increase
    Target_search:
      Columns:
        - Name: 'Wealth country'
          API_same_sector: wealth_country_reach
          API_diff_sector: wealth_country_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [gdp, income]
        Indicator:
          CONSOLIDATE: 'Wealth country'
          EXPAND_NEW_COUNTRIES: 'Wealth country'
          EXPAND_ADJACENT: 'Wealth country'

  - Name: 'Population_city_based_sectors'
    Sector_ids: [94]
    Snapshot_view:
      Maps:
        - Name: 'Population city'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: population_city_increase
          Indicator_API_diff_sector: population_city_increase
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Population city'
          API_same_sector: population_city_increase
          API_diff_sector: population_city_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Population city'
          EXPAND_NEW_COUNTRIES: 'Population city'
          EXPAND_ADJACENT: 'Population city'

  - Name: 'German tax advisors'
    Sector_ids: [153, 154]
    Snapshot_view:
      Maps:
        - Name: 'Population city'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: population_city_increase
          Indicator_API_diff_sector: population_city_increase
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Total employees'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Total employees est'
          API_same_sector: strip_profile_total_employees_est
          API_diff_sector: strip_profile_total_employees_est
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Professionals (website)'
          API_same_sector: strip_profile_professionals_website
          API_diff_sector: strip_profile_professionals_website
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Professionals (registry)'
          API_same_sector: strip_profile_professionals_registry
          API_diff_sector: strip_profile_professionals_registry
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Client focus'
          API_same_sector: strip_profile_client_focus
          API_diff_sector: strip_profile_client_focus
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Payroll'
          API_same_sector: strip_profile_payroll
          API_diff_sector: strip_profile_payroll
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Law'
          API_same_sector: strip_profile_law_focus
          API_diff_sector: strip_profile_law_focus
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Year founded'
          API_same_sector: strip_profile_year_founded
          API_diff_sector: strip_profile_year_founded
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Ownership'
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'GDP location'
          API_same_sector: gdp_location_increase
          API_diff_sector: gdp_location_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Company overlaps'
          API_same_sector: company_overlaps
          API_diff_sector: company_overlaps
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Revenue'
          API_same_sector: strip_profile_revenue
          API_diff_sector: strip_profile_revenue
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Revenue est'
          API_same_sector: strip_profile_revenue_est
          API_diff_sector: strip_profile_revenue_est
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Operating profit'
          API_same_sector: strip_profile_operating_profit
          API_diff_sector: strip_profile_operating_profit
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Company overlaps'
          EXPAND_NEW_COUNTRIES: 'Company overlaps'
          EXPAND_ADJACENT: 'Company overlaps'

  - Name: 'Construction services'
    Sector_ids: [23, 143, 163, 166]
    Snapshot_view:
      Maps:
        - Name: 'Population city'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: population_city_increase
          Indicator_API_diff_sector: population_city_increase
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Total employees'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Revenue est'
          API_same_sector: strip_profile_revenue_est
          API_diff_sector: strip_profile_revenue_est
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Product focus'
          API_same_sector: strip_profile_product_focus
          API_diff_sector: strip_profile_product_focus
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Year founded'
          API_same_sector: strip_profile_year_founded
          API_diff_sector: strip_profile_year_founded
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Ownership'
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Owner age'
          API_same_sector: strip_profile_owner_avg_age
          API_diff_sector: strip_profile_owner_avg_age
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'GDP location'
          API_same_sector: population_location_increase
          API_diff_sector: population_location_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Company overlaps'
          API_same_sector: company_overlaps
          API_diff_sector: company_overlaps
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Revenue'
          API_same_sector: strip_profile_revenue
          API_diff_sector: strip_profile_revenue
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Operating profit'
          API_same_sector: strip_profile_operating_profit
          API_diff_sector: strip_profile_operating_profit
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'ZfP'
          API_same_sector: strip_profile_explicit_zfp
          API_diff_sector: strip_profile_explicit_zfp
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Company overlaps'
          EXPAND_NEW_COUNTRIES: 'Company overlaps'
          EXPAND_ADJACENT: 'Company overlaps'

  - Name: 'Sample sector'
    Sector_ids: [155, 156]
    Snapshot_view:
      Maps:
        - Name: 'Population city'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: population_city_increase
          Indicator_API_diff_sector: population_city_increase
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Total employees est'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Total front-end employees'
          API_same_sector: strip_profile_fe_employees
          API_diff_sector: strip_profile_fe_employees
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Client focus'
          API_same_sector: strip_profile_client_focus
          API_diff_sector: strip_profile_client_focus
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Year founded'
          API_same_sector: strip_profile_year_founded
          API_diff_sector: strip_profile_year_founded
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Ownership'
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Revenue'
          API_same_sector: strip_profile_revenue
          API_diff_sector: strip_profile_revenue
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Revenue est'
          API_same_sector: strip_profile_revenue_est
          API_diff_sector: strip_profile_revenue_est
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Segment1'
          API_same_sector: strip_profile_segment1
          API_diff_sector: strip_profile_segment1
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Segment2'
          API_same_sector: strip_profile_segment2
          API_diff_sector: strip_profile_segment2
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'GDP location'
          API_same_sector: gdp_location_increase
          API_diff_sector: gdp_location_reach
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'GDP location'
          EXPAND_NEW_COUNTRIES: 'GDP location'
          EXPAND_ADJACENT: 'GDP location'


  - Name: 'Pharma Services and Pharmaceuticals'
    Sector_ids: [157, 158, 159, 31]
    Snapshot_view:
      Maps:
        - Name: 'Origination reach increase'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: gdp_country_increase
          Indicator_API_diff_sector: gdp_country_increase
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
    Target_search:
      Columns:
        - Name: 'Total employees'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Revenue est'
          API_same_sector: strip_profile_revenue_est
          API_diff_sector: strip_profile_revenue_est
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Client focus'
          show_only_for_sector: [157, 158, 159]
          API_same_sector: strip_profile_client_focus
          API_diff_sector: strip_profile_client_focus
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Year founded'
          API_same_sector: strip_profile_year_founded
          API_diff_sector: strip_profile_year_founded
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Ownership'
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'GDP location'
          API_same_sector: gdp_location_reach
          API_diff_sector: gdp_location_reach
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Company overlaps'
          API_same_sector: company_overlaps
          API_diff_sector: company_overlaps
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
          hide_no_buyer: True
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [gdp, income]
        Indicator:
          CONSOLIDATE: 'GDP country'
          EXPAND_NEW_COUNTRIES: 'GDP country'
          EXPAND_ADJACENT: 'GDP country'

  - Name: 'Healthcare Providers'
    Sector_ids: [18, 123, 124, 126, 127, 129, 147, 164, 25]
    Snapshot_view:
      Maps:
        - Name: 'Location overlap'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: company_overlaps
          Indicator_API_diff_sector: company_overlaps
        - Name: 'Country overlap'
          Color_countries_overlap: True
          Show_dots: True
          Indicator_API_same_sector: country_overlaps
          Indicator_API_diff_sector: country_overlaps
        - Name: 'Distribution reach expansion'
          Color_countries_overlap: False
          Show_dots: True
          Indicator_API_same_sector: population_location_increase
          Indicator_API_diff_sector: population_location_increase
    Target_search:
      Columns:
        - Name: 'Total employees'
          API_same_sector: strip_profile_total_employees
          API_diff_sector: strip_profile_total_employees
          show_for_strategies: [EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE]
        - Name: 'Revenue est'
          API_same_sector: strip_profile_revenue_est
          API_diff_sector: strip_profile_revenue_est
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Year founded'
          API_same_sector: strip_profile_year_founded
          API_diff_sector: strip_profile_year_founded
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Ownership'
          API_same_sector: strip_profile_ownership
          API_diff_sector: strip_profile_ownership
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'GDP location'
          API_same_sector: gdp_location_reach
          API_diff_sector: gdp_location_reach
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
        - Name: 'Company overlaps'
          API_same_sector: company_overlaps
          API_diff_sector: company_overlaps
          show_for_strategies: [ EXPAND_NEW_COUNTRIES, EXPAND_ADJACENT, CONSOLIDATE ]
          hide_no_buyer: True
      Map:
        Color_countries_overlap: True
        Show_dots: True
        Heatmap: [population, income]
        Indicator:
          CONSOLIDATE: 'Company overlaps'
          EXPAND_NEW_COUNTRIES: 'Population location'
          EXPAND_ADJACENT: 'Population location'

API_reference:
  country_overlaps:
    title: 'Country overlaps'
    multiply_by: 100.0
    round_to: 0
    append_text: '%'
    use_title_from_API: False
    url: /combinations/get_country_overlaps
    params:
      buyer_cib_id:
      target_cib_ids:

  company_overlaps:
    title: 'Location overlap'
    multiply_by: 100.0
    round_to: 0
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_company_overlaps
    params:
      buyer_cib_id:
      country_code: 0
      function_code: 0
      sector_id:
      target_cib_ids:

  insurance_brokers_data_employees:
    title: 'Employees (#)'
    use_title_from_API: False
    url: /company_specific_info/get_insurance_broker_data
    params:
      cib_ids:

  wealth_management_data_aum:
    title: 'AUM ($bn)'
    round_to: 1
    thousands_separator: ','
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'AUM'
      type: 'numeric'

  wealth_management_data_aum_client:
    title: 'Average AUM / client ($mm)'
    thousands_separator: ','
    round_to: 1
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'aum_per_client'
      type: 'numeric'

  wealth_management_data_num_clients:
    title: 'No. of clients'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'num_clients'
      type: 'numeric'

  wealth_management_data_intl_clients:
    title: 'Intl. clients share (%)'
    round_to: 0
    append_text: '%'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'international_clients'
      type: 'numeric'

  wealth_management_data_intl_locations:
    title: 'International locations'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'international_locations'
      type: 'numeric'

  banks_total_assets:
    title: 'Total assets ($b)'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'total_assets'
      type: 'numeric'

  banks_net_interest_income:
    title: 'Net interest income ($b)'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'net_interest_income'
      type: 'numeric'

  banks_non_interest_income:
    title: 'Non interest income ($b)'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'non_interest_income'
      type: 'numeric'

  banks_net_income:
    title: 'Net income ($b)'
    thousands_separator: ','
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'net_income'
      type: 'numeric'

  incremental_income:
    title: 'Average gross income of incremental consumers in [Country]'
    multiply_by: 1.0
    round_to: 1
    append_text: 'k'
    show_zero_decimal: True
    use_title_from_API: True
    url: /company_extended_metrics/get_incremental_income
    params:
      buyer_cib_id:
      country_code:
      target_cib_ids:

  population_location_reach:
    title: 'Population reach by location'
    multiply_by: 0.000001
    round_to: 1
    append_text: ''
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: POPULATION
      metric_geography: LOCATION
      cib_ids:

  population_location_increase:
    title: 'Consumer reach increase based on locations'
    multiply_by: 100.0
    round_to: 0
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: POPULATION
      metric_geography: LOCATION
      buyer_cib_id:
      target_cib_ids:

  population_country_reach:
    title: 'Population reach by country'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: POPULATION
      metric_geography: COUNTRY
      cib_ids:

  population_country_increase:
    title: 'Population increase by country (m)'
    multiply_by: 1.0
    round_to: 0
    append_text: 'm'
    use_title_from_API: True
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: POPULATION
      metric_geography: COUNTRY
      buyer_cib_id:
      target_cib_ids:

  gdp_country_reach:
    title: 'GDP reach by country'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: GDP
      metric_geography: COUNTRY
      cib_ids:

  gdp_country_increase:
    title: 'GDP increase by country'
    multiply_by: 100.0
    round_to: 1
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: GDP
      metric_geography: COUNTRY
      buyer_cib_id:
      target_cib_ids:

  gdp_location_reach:
    title: 'GDP reach by locations ($tn)'
    include_in_stats: False
    multiply_by: 1.0
    round_to: 2
    use_title_from_API: False
    show_zero_decimal: True
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: GDP
      metric_geography: LOCATION
      cib_ids:

  gdp_location_increase:
    title: 'GDP reach increase by locations'
    multiply_by: 100.0
    round_to: 1
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: GDP
      metric_geography: LOCATION
      buyer_cib_id:
      target_cib_ids:

  wealth_country_reach:
    title: 'Wealth reach by country'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: WEALTH
      metric_geography: COUNTRY
      cib_ids:

  wealth_country_increase:
    title: 'Wealth increase by country'
    multiply_by: 100.0
    round_to: 1
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: WEALTH
      metric_geography: COUNTRY
      buyer_cib_id:
      target_cib_ids:

  population_city_reach:
    title: 'Population reach by city'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_reach
    params:
      metric_type: POPULATION
      metric_geography: CITY
      cib_ids:

  population_city_increase:
    title: 'Population increase by city'
    multiply_by: 100.0
    round_to: 1
    append_text: '%'
    use_title_from_API: False
    url: /company_extended_metrics/get_metric_increase
    params:
      metric_type: POPULATION
      metric_geography: CITY
      buyer_cib_id:
      target_cib_ids:

  strip_profile_total_employees:
    title: 'Total employees (#)'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'total_employees'
      type: 'numeric'

  strip_profile_total_employees_est:
    title: 'Estimated employees (#)'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'total_employees_estimated'
      type: 'numeric'

  strip_profile_fe_employees:
    title: 'Total front-end employees (#)'
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'total_front_end_employees'
      type: 'numeric'

  strip_profile_professionals_website:
    title: 'Total professionals (#)'
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'total_professionals'
      type: 'numeric'

  strip_profile_professionals_registry:
    title: 'Tax advisor professionals (# registry)'
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'tax_professionals_registry'
      type: 'numeric'

  strip_profile_client_focus:
    title: 'Client focus'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'client_focus'
      type: 'str'

  strip_profile_owner_avg_age:
    title: 'Age / avg. age of owner(s)'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'owner_average_age'
      type: 'str'


  strip_profile_product_focus:
    title: 'Cable / pipeline focus'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'Product_focus'
      type: 'str'

  strip_profile_client_focus_short:
    title: 'Client focus'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'client_focus_short'
      type: 'str'

  strip_profile_law_focus:
    title: 'Law focus'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'law_focus'
      type: 'str'

  strip_profile_revenue_est:
    title: 'Estimated revenue (€m)'
    multiply_by: 0.000001
    round_to: 0
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'revenues_estimated'
      type: 'numeric'

  strip_profile_revenue:
    title: 'Revenue (€m)'
    round_to: 1
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'revenue'
      type: 'numeric'

  strip_profile_operating_profit:
    title: 'Pre-tax profit (€m)'
    round_to: 1
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'operating_profit'
      type: 'numeric'

  strip_profile_ownership:
    title: 'Ownership (Individuals/Legal entities/Both)'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'ownership_type_short'
      type: 'str'

  strip_profile_year_founded:
    title: 'Year founded'
    include_in_stats: False
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'firm_years_summary'
      type: 'numeric'

  strip_profile_payroll:
    title: 'Payroll services'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'payroll_focus'
      type: 'str'

  strip_profile_explicit_zfp:
    title: 'Explicit ZfP focus'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'explicit_zfp'
      type: 'str'

  strip_profile_hq:
    title: 'HQ'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'hq'
      type: 'str'

  strip_profile_segment1:
    title: 'Segment1'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'segment1_focus'
      type: 'str'

  strip_profile_segment2:
    title: 'Segment2'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'segment2_focus'
      type: 'str'

  strip_profile_advisory_employees:
    title: 'Advisory employees (#)'
    use_title_from_API: False
    url: /company_specific_info/get_strip_profile_data_simplified
    params:
      cib_ids:
      field: 'advisory_employees'
      type: 'numeric'
